#!/bin/bash

# Test runner script for the ELT pipeline

set -e

echo "🧪 Running tests for Ray Distributed Kafka Consumer with Daft and Iceberg"
echo "=================================================================="

# Check if pytest is installed
if ! command -v pytest &> /dev/null; then
    echo "❌ pytest not found. Installing..."
    pip install pytest pytest-cov
fi

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# Run unit tests
echo "📋 Running unit tests..."
pytest tests/ -v --tb=short

# Run tests with coverage
echo "📊 Running tests with coverage..."
pytest tests/ --cov=src --cov-report=html --cov-report=term-missing

echo "✅ All tests completed!"
echo "📄 Coverage report generated in htmlcov/index.html"
