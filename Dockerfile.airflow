# Dockerfile for Airflow with <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> support
FROM apache/airflow:2.10.4-python3.11

# Switch to root to install system dependencies
USER root

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
    && apt-get autoremove -yqq --purge \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Switch back to airflow user
USER airflow

# Copy requirements file
COPY requirements-dev.txt /requirements-dev.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r /requirements-dev.txt

# Copy source code
COPY --chown=airflow:root src/ /opt/airflow/src/
COPY --chown=airflow:root config/ /opt/airflow/config/

# Set Python path
ENV PYTHONPATH="/opt/airflow/src:${PYTHONPATH}"

# Create necessary directories
RUN mkdir -p /opt/airflow/logs /opt/airflow/plugins

# Set working directory
WORKDIR /opt/airflow
