# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

# Ray Configuration
RAY_HEAD_ADDRESS=ray://localhost:10001
LOCAL_MODE=Y
WORKER_NUM_CPUS=0.25

# Application Configuration
WORKER_CONFIG_PATH=config/consumer_config.json
APP_USERNAME=admin
APP_PASSWORD=admin

# MinIO Configuration (S3-compatible storage)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=false
MINIO_BUCKET_NAME=data-lake

# Nessie Configuration (Iceberg catalog)
NESSIE_URI=http://localhost:19120/api/v1
NESSIE_REF=main
NESSIE_AUTH_TYPE=NONE

# Iceberg Configuration
ICEBERG_WAREHOUSE_PATH=s3a://data-lake/warehouse
ICEBERG_CATALOG_TYPE=nessie
ICEBERG_CATALOG_URI=http://localhost:19120/api/v1

# Database Configuration (for Airflow)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=airflow
POSTGRES_USER=airflow
POSTGRES_PASSWORD=airflow

# Airflow Configuration
AIRFLOW_HOME=/opt/airflow
AIRFLOW__CORE__EXECUTOR=LocalExecutor
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@localhost:5432/airflow
AIRFLOW__CORE__FERNET_KEY=
AIRFLOW__WEBSERVER__SECRET_KEY=
AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=true
AIRFLOW__CORE__LOAD_EXAMPLES=false

# ELT Processing Configuration
BRONZE_BATCH_SIZE=1000
BRONZE_BATCH_TIMEOUT_SECONDS=30
SILVER_PROCESSING_SCHEDULE=0 2 * * *
GOLD_PROCESSING_SCHEDULE=0 4 * * *

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
