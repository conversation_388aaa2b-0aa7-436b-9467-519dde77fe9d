# Development and testing dependencies
-r requirements.txt

# Airflow for orchestration (separate from core dependencies)
apache-airflow==2.10.4
apache-airflow-providers-postgres==5.13.1
apache-airflow-providers-amazon==8.31.0

# Testing
pytest==8.3.4
pytest-asyncio==0.24.0
pytest-cov==6.0.0

# Code quality
black==24.10.0
flake8==7.1.1
isort==5.13.2
mypy==1.13.0

# Documentation
sphinx==8.1.3
sphinx-rtd-theme==3.0.2

# Jupyter for development
jupyter==1.1.1
jupyterlab==4.3.3
