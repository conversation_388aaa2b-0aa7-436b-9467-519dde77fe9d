"""
Tests for Iceberg utilities.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pyiceberg.schema import Schema
from pyiceberg.types import NestedField, StringType, LongType

from src.elt.iceberg_utils import IcebergCatalogManager, create_standard_bronze_schema


class TestIcebergCatalogManager:
    """Test cases for IcebergCatalogManager."""
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_initialization_with_config(self, mock_minio, mock_load_catalog):
        """Test initialization with custom config."""
        config = {
            'nessie_uri': 'http://test:19120/api/v1',
            'nessie_ref': 'test-branch',
            'warehouse_path': 's3a://test-bucket/warehouse',
            'minio_endpoint': 'test:9000',
            'minio_access_key': 'test-key',
            'minio_secret_key': 'test-secret',
            'minio_bucket': 'test-bucket'
        }
        
        # Mock catalog and minio client
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        # Initialize manager
        manager = IcebergCatalogManager(config)
        
        # Verify initialization
        assert manager.config == config
        assert manager.catalog == mock_catalog
        assert manager.minio_client == mock_minio_client
        
        # Verify catalog configuration
        mock_load_catalog.assert_called_once()
        call_args = mock_load_catalog.call_args
        assert call_args[0][0] == 'nessie_catalog'
        assert call_args[1]['uri'] == 'http://test:19120/api/v1'
        assert call_args[1]['ref'] == 'test-branch'
        assert call_args[1]['warehouse'] == 's3a://test-bucket/warehouse'
    
    @patch.dict('os.environ', {
        'NESSIE_URI': 'http://env:19120/api/v1',
        'MINIO_ENDPOINT': 'env:9000',
        'MINIO_BUCKET_NAME': 'env-bucket'
    })
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_initialization_from_env(self, mock_minio, mock_load_catalog):
        """Test initialization from environment variables."""
        # Mock catalog and minio client
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        # Initialize manager without config (should use env vars)
        manager = IcebergCatalogManager()
        
        # Verify environment variables were used
        assert manager.config['nessie_uri'] == 'http://env:19120/api/v1'
        assert manager.config['minio_endpoint'] == 'env:9000'
        assert manager.config['minio_bucket'] == 'env-bucket'
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_create_bronze_table(self, mock_minio, mock_load_catalog):
        """Test creating a bronze table."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        mock_table = Mock()
        mock_catalog.create_table.return_value = mock_table
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # Create bronze table
        result = manager.create_bronze_table("test_table", "bronze")
        
        # Verify table creation
        assert result == mock_table
        mock_catalog.create_namespace.assert_called_with("bronze")
        mock_catalog.create_table.assert_called_once()
        
        # Verify table creation parameters
        call_args = mock_catalog.create_table.call_args
        assert call_args[1]['identifier'] == "bronze.test_table"
        assert isinstance(call_args[1]['schema'], Schema)
        assert 'write.format.default' in call_args[1]['properties']
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_create_silver_table(self, mock_minio, mock_load_catalog):
        """Test creating a silver table."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        mock_table = Mock()
        mock_catalog.create_table.return_value = mock_table
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # Create test schema
        test_schema = Schema(
            NestedField(1, "id", LongType(), required=True),
            NestedField(2, "name", StringType(), required=True)
        )
        
        # Create silver table
        result = manager.create_silver_table("test_table", test_schema, "silver")
        
        # Verify table creation
        assert result == mock_table
        mock_catalog.create_namespace.assert_called_with("silver")
        mock_catalog.create_table.assert_called_once()
        
        # Verify table creation parameters
        call_args = mock_catalog.create_table.call_args
        assert call_args[1]['identifier'] == "silver.test_table"
        assert call_args[1]['schema'] == test_schema
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_get_table(self, mock_minio, mock_load_catalog):
        """Test getting an existing table."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        mock_table = Mock()
        mock_catalog.load_table.return_value = mock_table
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # Get table
        result = manager.get_table("test_table", "bronze")
        
        # Verify table retrieval
        assert result == mock_table
        mock_catalog.load_table.assert_called_once_with("bronze.test_table")
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_list_tables(self, mock_minio, mock_load_catalog):
        """Test listing tables in a namespace."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        expected_tables = ["table1", "table2", "table3"]
        mock_catalog.list_tables.return_value = expected_tables
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # List tables
        result = manager.list_tables("bronze")
        
        # Verify table listing
        assert result == expected_tables
        mock_catalog.list_tables.assert_called_once_with("bronze")
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_table_exists_true(self, mock_minio, mock_load_catalog):
        """Test table existence check when table exists."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        mock_table = Mock()
        mock_catalog.load_table.return_value = mock_table
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # Check table existence
        result = manager.table_exists("test_table", "bronze")
        
        # Verify result
        assert result is True
        mock_catalog.load_table.assert_called_once_with("bronze.test_table")
    
    @patch('src.elt.iceberg_utils.load_catalog')
    @patch('src.elt.iceberg_utils.Minio')
    def test_table_exists_false(self, mock_minio, mock_load_catalog):
        """Test table existence check when table doesn't exist."""
        # Setup mocks
        mock_catalog = Mock()
        mock_load_catalog.return_value = mock_catalog
        
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = True
        mock_minio.return_value = mock_minio_client
        
        # Make load_table raise an exception
        mock_catalog.load_table.side_effect = Exception("Table not found")
        
        # Initialize manager
        manager = IcebergCatalogManager()
        
        # Check table existence
        result = manager.table_exists("test_table", "bronze")
        
        # Verify result
        assert result is False
        mock_catalog.load_table.assert_called_once_with("bronze.test_table")
    
    @patch('src.elt.iceberg_utils.Minio')
    def test_minio_bucket_creation(self, mock_minio):
        """Test MinIO bucket creation when bucket doesn't exist."""
        # Setup mocks
        mock_minio_client = Mock()
        mock_minio_client.bucket_exists.return_value = False
        mock_minio.return_value = mock_minio_client
        
        with patch('src.elt.iceberg_utils.load_catalog'):
            # Initialize manager
            manager = IcebergCatalogManager()
            
            # Verify bucket creation
            mock_minio_client.bucket_exists.assert_called_once()
            mock_minio_client.make_bucket.assert_called_once()


def test_create_standard_bronze_schema():
    """Test creating standard bronze schema."""
    schema = create_standard_bronze_schema()
    
    # Verify schema structure
    assert isinstance(schema, Schema)
    
    # Check required fields
    field_names = [field.name for field in schema.fields]
    expected_fields = [
        "key", "value", "topic", "partition", "offset",
        "timestamp", "kafka_timestamp", "processing_time",
        "year", "month", "day"
    ]
    
    for field in expected_fields:
        assert field in field_names
    
    # Verify field types
    field_by_name = {field.name: field for field in schema.fields}
    assert isinstance(field_by_name["key"].field_type, StringType)
    assert isinstance(field_by_name["value"].field_type, StringType)
    assert isinstance(field_by_name["partition"].field_type, LongType)
    assert isinstance(field_by_name["offset"].field_type, LongType)
