"""
Tests for DaftIcebergTransformer.
"""

import json
import pytest
from datetime import datetime, timezone
from unittest.mock import Mock

from kafka.consumer.fetcher import ConsumerRecord

from src.transformers.daft_iceberg_transformer import DaftIcebergTransformer
from src.model.worker_dto import Sink<PERSON><PERSON>ordD<PERSON>, SinkOperationType


class TestDaftIcebergTransformer:
    """Test cases for DaftIcebergTransformer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = {
            'add_kafka_metadata': True,
            'add_processing_time': True,
            'parse_json_value': True,
            'flatten_json': False
        }
        self.transformer = DaftIcebergTransformer(self.config)
    
    def test_transform_valid_json_message(self):
        """Test transforming a valid JSON message."""
        # Create mock consumer record
        message_data = {"user_id": 123, "action": "login", "timestamp": "2024-01-15T10:30:00Z"}
        consumer_record = ConsumerRecord(
            topic="user-events",
            partition=1,
            offset=12345,
            timestamp=1705315800000,  # 2024-01-15T10:30:00Z in milliseconds
            timestamp_type=1,
            key="user_123",
            value=json.dumps(message_data),
            checksum=None,
            serialized_key_size=8,
            serialized_value_size=len(json.dumps(message_data)),
            headers=[]
        )
        
        # Transform the record
        result = self.transformer.transform(consumer_record)
        
        # Assertions
        assert isinstance(result, SinkRecordDTO)
        assert result.key == "user_123"
        assert result.topic == "user-events"
        assert result.partition == 1
        assert result.offset == 12345
        assert result.sink_operation.sink_operation_type == SinkOperationType.UPSERT
        
        # Check message content
        message = result.message
        assert message["user_id"] == 123
        assert message["action"] == "login"
        assert message["timestamp"] == "2024-01-15T10:30:00Z"
        
        # Check metadata
        assert message["_kafka_topic"] == "user-events"
        assert message["_kafka_partition"] == 1
        assert message["_kafka_offset"] == 12345
        assert message["_kafka_key"] == "user_123"
        assert "_kafka_timestamp" in message
        assert "_processing_time" in message
        assert "_year" in message
        assert "_month" in message
        assert "_day" in message
    
    def test_transform_invalid_json_message(self):
        """Test transforming an invalid JSON message."""
        consumer_record = ConsumerRecord(
            topic="user-events",
            partition=1,
            offset=12345,
            timestamp=1705315800000,
            timestamp_type=1,
            key="user_123",
            value="invalid json {",
            checksum=None,
            serialized_key_size=8,
            serialized_value_size=13,
            headers=[]
        )
        
        # Transform the record
        result = self.transformer.transform(consumer_record)
        
        # Should handle invalid JSON gracefully
        assert isinstance(result, SinkRecordDTO)
        assert result.message["value"] == "invalid json {"
        assert "_kafka_topic" in result.message
    
    def test_transform_empty_message(self):
        """Test transforming an empty message."""
        consumer_record = ConsumerRecord(
            topic="user-events",
            partition=1,
            offset=12345,
            timestamp=1705315800000,
            timestamp_type=1,
            key="user_123",
            value="",
            checksum=None,
            serialized_key_size=8,
            serialized_value_size=0,
            headers=[]
        )
        
        # Transform the record
        result = self.transformer.transform(consumer_record)
        
        # Should handle empty message gracefully
        assert isinstance(result, SinkRecordDTO)
        assert result.message == {}  # Empty JSON becomes empty dict
        assert "_kafka_topic" in result.message
    
    def test_transform_without_metadata(self):
        """Test transforming with metadata disabled."""
        config = {
            'add_kafka_metadata': False,
            'add_processing_time': False,
            'parse_json_value': True
        }
        transformer = DaftIcebergTransformer(config)
        
        message_data = {"user_id": 123, "action": "login"}
        consumer_record = ConsumerRecord(
            topic="user-events",
            partition=1,
            offset=12345,
            timestamp=1705315800000,
            timestamp_type=1,
            key="user_123",
            value=json.dumps(message_data),
            checksum=None,
            serialized_key_size=8,
            serialized_value_size=len(json.dumps(message_data)),
            headers=[]
        )
        
        result = transformer.transform(consumer_record)
        
        # Should not have metadata fields
        message = result.message
        assert message["user_id"] == 123
        assert message["action"] == "login"
        assert "_kafka_topic" not in message
        assert "_processing_time" not in message
    
    def test_create_daft_dataframe(self):
        """Test creating Daft DataFrame from records."""
        records = [
            {
                "user_id": 123,
                "action": "login",
                "_processing_time": datetime.now(timezone.utc).isoformat(),
                "_year": 2024,
                "_month": 1,
                "_day": 15
            },
            {
                "user_id": 124,
                "action": "logout",
                "_processing_time": datetime.now(timezone.utc).isoformat(),
                "_year": 2024,
                "_month": 1,
                "_day": 15
            }
        ]
        
        df = self.transformer.create_daft_dataframe(records)
        
        # Check DataFrame properties
        assert len(df) == 2
        assert "user_id" in df.column_names
        assert "action" in df.column_names
        assert "_processing_time" in df.column_names
        assert "_year" in df.column_names
    
    def test_create_empty_dataframe(self):
        """Test creating DataFrame from empty records."""
        df = self.transformer.create_daft_dataframe([])
        
        # Should handle empty list gracefully
        assert len(df) == 0
    
    def test_get_bronze_table_name(self):
        """Test bronze table name generation."""
        assert self.transformer.get_bronze_table_name("user-events") == "bronze_user_events"
        assert self.transformer.get_bronze_table_name("transaction.logs") == "bronze_transaction_logs"
        assert self.transformer.get_bronze_table_name("system_metrics") == "bronze_system_metrics"
    
    def test_validate_message_valid(self):
        """Test message validation with valid data."""
        message_data = {
            "_processing_time": datetime.now(timezone.utc).isoformat(),
            "_year": 2024,
            "_month": 1,
            "_day": 15,
            "user_id": 123
        }
        
        assert self.transformer.validate_message(message_data) is True
    
    def test_validate_message_invalid_year(self):
        """Test message validation with invalid year."""
        message_data = {
            "_processing_time": datetime.now(timezone.utc).isoformat(),
            "_year": 1999,  # Invalid year
            "_month": 1,
            "_day": 15
        }
        
        assert self.transformer.validate_message(message_data) is False
    
    def test_validate_message_missing_fields(self):
        """Test message validation with missing required fields."""
        message_data = {
            "user_id": 123
            # Missing required fields
        }
        
        assert self.transformer.validate_message(message_data) is False
    
    def test_parse_json_value_disabled(self):
        """Test with JSON parsing disabled."""
        config = {
            'parse_json_value': False,
            'add_kafka_metadata': True,
            'add_processing_time': True
        }
        transformer = DaftIcebergTransformer(config)
        
        consumer_record = ConsumerRecord(
            topic="user-events",
            partition=1,
            offset=12345,
            timestamp=1705315800000,
            timestamp_type=1,
            key="user_123",
            value='{"user_id": 123}',
            checksum=None,
            serialized_key_size=8,
            serialized_value_size=15,
            headers=[]
        )
        
        result = transformer.transform(consumer_record)
        
        # Should store as raw string
        assert result.message["value"] == '{"user_id": 123}'
        assert "_kafka_topic" in result.message
