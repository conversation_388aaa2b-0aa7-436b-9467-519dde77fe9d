## Distributed Kafka Consumer Using Ray
Using this project you can create a distributed Kafka Consumers, with the specified number of 
consumers that run on multiple nodes and provides an API support to manage your consumers. 
Operations like - starting/stopping
consumers.

This project uses [<PERSON>](https://docs.ray.io/) to create distributed kafka Consumers

### System Requirements:
Python Version: 3.7

Ray version: 1.8.0

### Setup Instructions

**<ins>Step 1 - Create Your Transformer Class</ins>**

To create a new transformer implement the abstract class [StreamTransformer](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/src/transformers/transformer.py) and use 
this new transformer in [worker config](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/config/consumer_config.json).

One example transformer is defined [here](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/src/transformers/test_transformer.py)

**<ins>Step 2 - Create your worker config</ins>**

One Example config is defined [here](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/config/consumer_config.json). 
```json
[
  {
    "consumer_name": "some_consumer_group_name",
    "topic_name": "first-topic",
    "number_of_workers": 2,
    "enable_auto_commit": false,
    "bootstrap_servers": "localhost:9092",
    "key_deserializer": "STRING_DES",
    "value_deserializer": "STRING_DES",
    "header_deserializer": null,
    "auto_offset_reset": "earliest",
    "max_poll_records": 20,
    "max_poll_interval_ms": 60000,
    "sink_configs": {
      "transformer_cls": "src.transformers.test_transformer.SampleTransformer",
      "num_retries": 3,
      "retry_delay_seconds": 1,
      "stream_writers": [
        "src.stream_writers.console_stream_writer.ConsoleStreamWriter"
      ]
    },
    "dlq_config": {
      "bootstrap_servers": "localhost:9092",
      "topic_name": "test-dlq",
      "key_serializer": "STRING_SER",
      "value_serializer": "STRING_SER",
      "acks": "all",
      "compression_type": "gzip",
      "retries": 3,
      "linger_ms": 10
    }
  }
]

```

Config info

Config Name|Description|default value|Is mandatory?|
-----------|-----------|------------|--------------|
consumer_name|This will be used as consumer group name| |Yes
number_of_workers|Number of consumers to create for a consumer group|1|No
sink_configs|Any config related to your sink task. Say, if your are writing to Elasticsearch then you may want to add ES endpoint in config| |Yes
dlq_config|Dead letter queue config| |No
For available Serializers/deserializers refer [ser_des_util.py](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/src/kafka_core/ser_des_util.py)

Rest of the configs are self explanatory. 

**<ins>Step 3 - Install the Requirements</ins>**

Install all dependencies in [requirement.txt](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/requirements.txt)
```shell
pip install -r <path/to/requirement.txt>
```

Install the code using `setup.py`.
This is needed for ray to find modules to pickle/unpickle.

Go to project root folder, where setup.py exists and run:
```shell
 pip install -e .
```

**<ins>Step 4 - Start ray head node</ins>**

If running in local, run below command:
```shell
 ray start --head --port=6379
```


**<ins>Step 5 - Set necessary Environment Variables</ins>**

Variable Name|Description|Is Mandatory?|Default Value|
-------------|------------|------------|-------------|
LOCAL_MODE| `Y` or `N`. Tells weather to run Kafka Consumer in single node or in a distributed setup.|N|Y|
RAY_HEAD_ADDRESS|Ex: `ray://************:10001`. Avoid creating this env variable, if head and driver/app running on same node|No|auto|
WORKER_CONFIG_PATH|worker [json conig](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/config/consumer_config.json) path|Yes||
APP_USERNAME|Username to setup Basic API Authentication|No|admin|
APP_PASSWORD|Password to setup Basic API Authentication|No|admin|
WORKER_NUM_CPUS|Number of CPUs to reserve per Consumer/Worker|No|0.25|
SECURITY_PROTOCOL|Pass the security protocol being used to connect to Kafka Brokers. Valid values are - PLAINTEXT, SASL_PLAINTEXT, SASL_SSL|No|None|
SASL_MECHANISM|Using SASL based Auth. Pass either of the valid values - PLAIN, SCRAM-SHA-256, SCRAM-SHA-512|No|None|
SASL_USERNAME|Pass SASL username if using SASL Auth to connect to Kafka|No|None|
SASL_PASSWORD|Pass SASL password if using SASL Auth to connect to Kafka|No|None

**<ins>Step 6 - Run the APP</ins>**
```shell
uvicorn src.event_consumer_app:app --port <port> --reload
```

**Run App in docker container**

<ins>Build Image</ins>
```shell
# run below in the project root folder
 build -t kafka-connect-ray .
```

<ins>Run Image</ins>
```shell
# add other environment variables as you need.
 docker run -e RAY_HEAD_ADDRESS=ray://localhost:10001 -e LOCAL_MODE=N  -dp 8002:8002 kafka-connect-ray
```

**IMPORTANT!!!!**

While creating ray cluster make sure to install code dependencies by running below command in 
your Node or VM or container:
```shell
pip install kafka-connect-dependency==0.1.1
```
This will let ray head and worker nodes find the modules. 

This setup is added in Ray K8 [cluster config yaml](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/k8/ray/ray-cluster-config.yaml#L74) file.

### License

The MIT License (MIT)

Copyright (c) Bikas Katwal - <EMAIL>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
associated documentation files (the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge, publish, distribute,
sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT
NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES
OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


# How data flows:

## Kafka to Iceberg Data Flow with Ray and Daft

```mermaid
graph TD
    %% External Systems
    K[Kafka Topic<br/>Raw JSON Messages] --> CWM
    
    %% Entry Point
    API[FastAPI App<br/>event_consumer_app.py] --> CWM[ConsumerWorkerManager<br/>consumer_manager.py]
    
    %% Ray Cluster Setup
    CWM --> RC{Ray Cluster<br/>Initialization}
    RC -->|Local Mode| RL[Ray Local]
    RC -->|Distributed| RD[Ray Distributed<br/>Multiple Nodes]
    
    %% Worker Creation
    CWM --> CW1[Ray Actor<br/>ConsumerWorker-1<br/>@ray.remote]
    CWM --> CW2[Ray Actor<br/>ConsumerWorker-2<br/>@ray.remote]
    CWM --> CWN[Ray Actor<br/>ConsumerWorker-N<br/>@ray.remote]
    
    %% Kafka Consumption
    CW1 --> KC1[KafkaConsumer.poll<br/>timeout_ms=1000]
    CW2 --> KC2[KafkaConsumer.poll<br/>timeout_ms=1000]
    CWN --> KCN[KafkaConsumer.poll<br/>timeout_ms=1000]
    
    %% Message Processing
    KC1 --> ST1[SinkTask.process<br/>List[ConsumerRecord]]
    KC2 --> ST2[SinkTask.process<br/>List[ConsumerRecord]]
    KCN --> STN[SinkTask.process<br/>List[ConsumerRecord]]
    
    %% Transformation Layer
    ST1 --> T1[DaftIcebergTransformer<br/>.transform]
    ST2 --> T2[DaftIcebergTransformer<br/>.transform]
    STN --> TN[DaftIcebergTransformer<br/>.transform]
    
    %% Data Enrichment
    T1 --> E1[Enrich with Metadata<br/>_kafka_offset<br/>_kafka_partition<br/>_kafka_timestamp<br/>_processing_time]
    T2 --> E2[Enrich with Metadata<br/>_kafka_offset<br/>_kafka_partition<br/>_kafka_timestamp<br/>_processing_time]
    TN --> EN[Enrich with Metadata<br/>_kafka_offset<br/>_kafka_partition<br/>_kafka_timestamp<br/>_processing_time]
    
    %% SinkRecordDTO Creation
    E1 --> DTO1[SinkRecordDTO<br/>key, message, metadata]
    E2 --> DTO2[SinkRecordDTO<br/>key, message, metadata]
    EN --> DTON[SinkRecordDTO<br/>key, message, metadata]
    
    %% Stream Writer Layer
    DTO1 --> SW1[BatchedDaftIcebergWriter<br/>.write]
    DTO2 --> SW2[BatchedDaftIcebergWriter<br/>.write]
    DTON --> SWN[BatchedDaftIcebergWriter<br/>.write]
    
    %% Batching Logic
    SW1 --> B1{Batch Ready?<br/>Size >= 1000 OR<br/>Timeout >= 30s}
    SW2 --> B2{Batch Ready?<br/>Size >= 1000 OR<br/>Timeout >= 30s}
    SWN --> BN{Batch Ready?<br/>Size >= 1000 OR<br/>Timeout >= 30s}
    
    %% Batch Processing
    B1 -->|Yes| FB1[_flush_batch]
    B2 -->|Yes| FB2[_flush_batch]
    BN -->|Yes| FBN[_flush_batch]
    
    B1 -->|No| BUFF1[Buffer Records<br/>current_batch.extend]
    B2 -->|No| BUFF2[Buffer Records<br/>current_batch.extend]
    BN -->|No| BUFFN[Buffer Records<br/>current_batch.extend]
    
    %% Daft DataFrame Creation
    FB1 --> DF1[daft.from_pylist<br/>Create DataFrame]
    FB2 --> DF2[daft.from_pylist<br/>Create DataFrame]
    FBN --> DFN[daft.from_pylist<br/>Create DataFrame]
    
    %% Daft Transformations
    DF1 --> DT1[Daft Transformations<br/>df.with_column year<br/>df.with_column month<br/>df.filter, df.select]
    DF2 --> DT2[Daft Transformations<br/>df.with_column year<br/>df.with_column month<br/>df.filter, df.select]
    DFN --> DTN[Daft Transformations<br/>df.with_column year<br/>df.with_column month<br/>df.filter, df.select]
    
    %% Iceberg Integration
    DT1 --> IC1[Iceberg Catalog<br/>load_table]
    DT2 --> IC2[Iceberg Catalog<br/>load_table]
    DTN --> ICN[Iceberg Catalog<br/>load_table]
    
    %% Final Write
    IC1 --> IW1[df.write_iceberg<br/>mode=append<br/>Partitioned Write]
    IC2 --> IW2[df.write_iceberg<br/>mode=append<br/>Partitioned Write]
    ICN --> IWN[df.write_iceberg<br/>mode=append<br/>Partitioned Write]
    
    %% Storage Layer
    IW1 --> IT[Iceberg Table<br/>my_database.kafka_events<br/>Partitioned by year/month]
    IW2 --> IT
    IWN --> IT
    
    %% Error Handling
    ST1 --> EH1{Transform<br/>Success?}
    ST2 --> EH2{Transform<br/>Success?}
    STN --> EHN{Transform<br/>Success?}
    
    EH1 -->|No| DLQ1[Dead Letter Queue<br/>KafkaStreamWriter]
    EH2 -->|No| DLQ2[Dead Letter Queue<br/>KafkaStreamWriter]
    EHN -->|No| DLQN[Dead Letter Queue<br/>KafkaStreamWriter]
    
    SW1 --> WE1{Write<br/>Success?}
    SW2 --> WE2{Write<br/>Success?}
    SWN --> WEN{Write<br/>Success?}
    
    WE1 -->|No| DLQ1
    WE2 -->|No| DLQ2
    WEN -->|No| DLQN
    
    %% Commit
    IW1 --> COM1[consumer.commit]
    IW2 --> COM2[consumer.commit]
    IWN --> COMN[consumer.commit]
    
    %% Management APIs
    API --> MGMT[Management Endpoints<br/>/manager/start-consumers<br/>/manager/stop-consumers<br/>/manager/health]
    
    %% Styling
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef ray fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef daft fill:#96ceb4,stroke:#333,stroke-width:2px,color:#fff
    classDef iceberg fill:#feca57,stroke:#333,stroke-width:2px,color:#000
    classDef error fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#000
    classDef api fill:#54a0ff,stroke:#333,stroke-width:2px,color:#fff
    
    class K kafka
    class CW1,CW2,CWN,RC,RL,RD ray
    class ST1,ST2,STN,T1,T2,TN,E1,E2,EN,DTO1,DTO2,DTON processing
    class DF1,DF2,DFN,DT1,DT2,DTN daft
    class IC1,IC2,ICN,IW1,IW2,IWN,IT iceberg
    class DLQ1,DLQ2,DLQN,EH1,EH2,EHN,WE1,WE2,WEN error
    class API,MGMT api
```

## Detailed Method Call Sequence - Kafka to Iceberg Processing
```mermaid
sequenceDiagram
    participant K as Kafka Topic
    participant API as FastAPI App
    participant CWM as ConsumerWorkerManager
    participant CW as ConsumerWorker (Ray Actor)
    participant KC as KafkaConsumer
    participant ST as SinkTask
    participant T as DaftIcebergTransformer
    participant SW as BatchedDaftIcebergWriter
    participant D as Daft DataFrame
    participant IC as Iceberg Catalog
    participant IT as Iceberg Table
    participant DLQ as Dead Letter Queue

    Note over API,CWM: Application Startup
    API->>CWM: cwm.start_all_workers()
    CWM->>CWM: init_container()
    CWM->>CW: ConsumerWorker.options(name=w_name).remote(config, w_name)
    CWM->>CW: worker_actor.run.remote()
    
    Note over CW,KC: Consumer Initialization
    CW->>KC: KafkaConsumer(bootstrap_servers, group_id, deserializers)
    CW->>KC: consumer.subscribe([topic_name])
    CW->>ST: SinkTask(config)
    ST->>T: get_transformer(transformer_cls_path, sink_configs)
    ST->>SW: get_stream_writers(stream_writer_cls_paths, sink_configs)
    
    Note over K,IT: Main Processing Loop
    loop Every poll_timeout_ms (1000ms)
        CW->>KC: consumer.poll(timeout_ms=1000)
        KC->>K: Fetch messages
        K-->>KC: List[ConsumerRecord]
        KC-->>CW: tp_records_dict
        
        alt Records available
            CW->>ST: sink_task.process(consumer_records)
            
            loop For each ConsumerRecord
                ST->>T: stream_transformer.transform(consumer_record)
                
                Note over T: Data Transformation
                T->>T: json.loads(consumer_record.value)
                T->>T: Enrich with metadata:<br/>_kafka_offset, _kafka_partition,<br/>_kafka_timestamp, _processing_time
                T-->>ST: SinkRecordDTO(key, enriched_message, metadata)
                
                alt Transform Success
                    ST->>SW: write_to_sink([sink_record_dto])
                    SW->>SW: current_batch.extend(streams)
                    
                    alt Batch Ready (size >= 1000 OR timeout >= 30s)
                        SW->>SW: _flush_batch()
                        
                        Note over SW,D: Daft Processing
                        SW->>D: daft.from_pylist([record.message for record in current_batch])
                        D-->>SW: DataFrame
                        
                        SW->>D: df.with_column("year", df["timestamp"].dt.year())
                        SW->>D: df.with_column("month", df["timestamp"].dt.month())
                        SW->>D: df.filter(...).select(...)
                        D-->>SW: Transformed DataFrame
                        
                        Note over SW,IT: Iceberg Write
                        SW->>IC: catalog.load_table(table_name)
                        IC-->>SW: Iceberg Table Reference
                        SW->>D: df.write_iceberg(table, mode="append")
                        D->>IT: Write partitioned data (year/month)
                        IT-->>D: Write confirmation
                        D-->>SW: Write success
                        
                        SW->>SW: current_batch = []
                        SW->>SW: last_write_time = time.time()
                    else
                        Note over SW: Buffer records for next batch
                    end
                    
                else Transform Failed
                    ST->>DLQ: handle_dlq_push(key, message, topic, partition, 'TRANSFORM', error, offset)
                    DLQ->>DLQ: Create DeadLetterDTO
                    DLQ->>DLQ: dlq_stream_writer.write([dead_letter])
                end
                
                alt Write Failed
                    ST->>DLQ: handle_dlq_push(key, message, topic, partition, 'SINK_UPDATE', error, offset)
                end
            end
            
            Note over CW,KC: Commit Offset
            CW->>KC: consumer.commit()
            KC-->>CW: Commit confirmation
        end
        
        alt Stop Signal Received
            CW->>KC: consumer.close()
            CW->>CW: is_closed = True
            break
        end
    end
    
    Note over API: Management Operations
    API->>CWM: /manager/stop-consumers
    CWM->>CW: ray.get(worker_actor.stop_consumer.remote())
    CW->>CW: stop_worker = True
    CWM->>CW: ray.kill(worker_actor)
    
    Note over SW: Cleanup on Shutdown
    SW->>SW: _flush_batch() # Flush remaining records
    SW->>D: Final write_iceberg() if batch not empty
```

## Kafka to Iceberg Data Flow Architecture
```mermaid
graph TB
    %% External Data Source
    subgraph "Data Source"
        K1[Kafka Topic 1<br/>user_events]
        K2[Kafka Topic 2<br/>transaction_logs]
        K3[Kafka Topic N<br/>system_metrics]
    end
    
    %% Management Layer
    subgraph "Management & Control Layer"
        API[FastAPI Application<br/>event_consumer_app.py<br/>Port: 8000]
        MGMT[Management APIs<br/>POST /manager/start-consumers<br/>POST /manager/stop-consumers<br/>GET /manager/fetch-consumers<br/>POST /manager/read-from-timestamp]
        AUTH[Basic Authentication<br/>Username/Password]
    end
    
    %% Ray Cluster
    subgraph "Ray Distributed Computing Cluster"
        direction TB
        HEAD[Ray Head Node<br/>ray://localhost:10001]
        
        subgraph "Ray Workers Pool"
            direction LR
            NODE1[Ray Worker Node 1<br/>CPU: 4 cores]
            NODE2[Ray Worker Node 2<br/>CPU: 4 cores]
            NODE3[Ray Worker Node N<br/>CPU: 4 cores]
        end
        
        subgraph "Consumer Workers (Ray Actors)"
            direction TB
            CW1["@ray.remote<br/>ConsumerWorker-1<br/>Group: user_events<br/>CPU: 0.25"]
            CW2["@ray.remote<br/>ConsumerWorker-2<br/>Group: user_events<br/>CPU: 0.25"]
            CW3["@ray.remote<br/>ConsumerWorker-3<br/>Group: transaction_logs<br/>CPU: 0.25"]
            CW4["@ray.remote<br/>ConsumerWorker-N<br/>Group: system_metrics<br/>CPU: 0.25"]
        end
    end
    
    %% Processing Pipeline
    subgraph "Data Processing Pipeline"
        direction TB
        
        subgraph "Kafka Consumer Layer"
            KC1[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20<br/>Auto Commit: false]
            KC2[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20<br/>Auto Commit: false]
            KC3[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20<br/>Auto Commit: false]
        end
        
        subgraph "Message Processing"
            ST1[SinkTask.process<br/>Rate Limit: 20 calls/sec]
            ST2[SinkTask.process<br/>Rate Limit: 20 calls/sec]
            ST3[SinkTask.process<br/>Rate Limit: 20 calls/sec]
        end
        
        subgraph "Data Transformation"
            T1[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add: _kafka_offset<br/>Add: _kafka_partition<br/>Add: _kafka_timestamp<br/>Add: _processing_time]
            T2[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add: _kafka_offset<br/>Add: _kafka_partition<br/>Add: _kafka_timestamp<br/>Add: _processing_time]
            T3[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add: _kafka_offset<br/>Add: _kafka_partition<br/>Add: _kafka_timestamp<br/>Add: _processing_time]
        end
        
        subgraph "DTO Creation"
            DTO1[SinkRecordDTO<br/>key: string<br/>message: dict<br/>metadata: dict<br/>sink_operation: UPSERT]
            DTO2[SinkRecordDTO<br/>key: string<br/>message: dict<br/>metadata: dict<br/>sink_operation: UPSERT]
            DTO3[SinkRecordDTO<br/>key: string<br/>message: dict<br/>metadata: dict<br/>sink_operation: UPSERT]
        end
    end
    
    %% Batching and Daft Processing
    subgraph "Batching & Columnar Processing"
        direction TB
        
        subgraph "Stream Writers"
            SW1[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s<br/>Buffer: current_batch[]]
            SW2[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s<br/>Buffer: current_batch[]]
            SW3[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s<br/>Buffer: current_batch[]]
        end
        
        subgraph "Batch Decision"
            B1{Batch Ready?<br/>Size >= 1000 OR<br/>Time >= 30s}
            B2{Batch Ready?<br/>Size >= 1000 OR<br/>Time >= 30s}
            B3{Batch Ready?<br/>Size >= 1000 OR<br/>Time >= 30s}
        end
        
        subgraph "Daft DataFrames"
            DF1[daft.from_pylist<br/>DataFrame Creation<br/>Columnar Format]
            DF2[daft.from_pylist<br/>DataFrame Creation<br/>Columnar Format]
            DF3[daft.from_pylist<br/>DataFrame Creation<br/>Columnar Format]
        end
        
        subgraph "Daft Transformations"
            DT1[df.with_column year<br/>df.with_column month<br/>df.with_column day<br/>df.filter active_users<br/>df.select relevant_cols]
            DT2[df.with_column year<br/>df.with_column month<br/>df.with_column day<br/>df.filter valid_transactions<br/>df.select relevant_cols]
            DT3[df.with_column year<br/>df.with_column month<br/>df.with_column day<br/>df.filter system_health<br/>df.select relevant_cols]
        end
    end
    
    %% Iceberg Storage Layer
    subgraph "Iceberg Data Lake"
        direction TB
        
        subgraph "Iceberg Catalog"
            CAT[Iceberg Catalog<br/>Type: Hive Metastore<br/>URI: thrift://localhost:9083]
        end
        
        subgraph "Iceberg Tables"
            IT1[Table: events.user_events<br/>Partitioned by: year, month<br/>Format: Parquet<br/>Compression: ZSTD]
            IT2[Table: events.transaction_logs<br/>Partitioned by: year, month<br/>Format: Parquet<br/>Compression: ZSTD]
            IT3[Table: events.system_metrics<br/>Partitioned by: year, month<br/>Format: Parquet<br/>Compression: ZSTD]
        end
        
        subgraph "Storage Backend"
            S3[AWS S3 / MinIO<br/>Bucket: data-lake<br/>Path: /iceberg/warehouse/]
        end
    end
    
    %% Error Handling
    subgraph "Error Handling & Monitoring"
        direction TB
        
        subgraph "Dead Letter Queue"
            DLQ1[DLQ Topic: user_events_dlq<br/>Failed Transforms<br/>Failed Writes]
            DLQ2[DLQ Topic: transaction_logs_dlq<br/>Failed Transforms<br/>Failed Writes]
            DLQ3[DLQ Topic: system_metrics_dlq<br/>Failed Transforms<br/>Failed Writes]
        end
        
        subgraph "Monitoring"
            METRICS[Ray Dashboard<br/>Worker Health<br/>Processing Metrics<br/>Error Rates]
            LOGS[Application Logs<br/>Processing Status<br/>Error Details]
        end
    end
    
    %% Data Flow Connections
    K1 --> CW1
    K1 --> CW2
    K2 --> CW3
    K3 --> CW4
    
    API --> MGMT
    MGMT --> AUTH
    API --> HEAD
    
    HEAD --> NODE1
    HEAD --> NODE2
    HEAD --> NODE3
    
    NODE1 --> CW1
    NODE1 --> CW2
    NODE2 --> CW3
    NODE3 --> CW4
    
    CW1 --> KC1
    CW2 --> KC2
    CW3 --> KC3
    
    KC1 --> ST1
    KC2 --> ST2
    KC3 --> ST3
    
    ST1 --> T1
    ST2 --> T2
    ST3 --> T3
    
    T1 --> DTO1
    T2 --> DTO2
    T3 --> DTO3
    
    DTO1 --> SW1
    DTO2 --> SW2
    DTO3 --> SW3
    
    SW1 --> B1
    SW2 --> B2
    SW3 --> B3
    
    B1 -->|Yes| DF1
    B2 -->|Yes| DF2
    B3 -->|Yes| DF3
    
    DF1 --> DT1
    DF2 --> DT2
    DF3 --> DT3
    
    DT1 --> CAT
    DT2 --> CAT
    DT3 --> CAT
    
    CAT --> IT1
    CAT --> IT2
    CAT --> IT3
    
    IT1 --> S3
    IT2 --> S3
    IT3 --> S3
    
    %% Error flows
    T1 -.->|Transform Error| DLQ1
    T2 -.->|Transform Error| DLQ2
    T3 -.->|Transform Error| DLQ3
    
    SW1 -.->|Write Error| DLQ1
    SW2 -.->|Write Error| DLQ2
    SW3 -.->|Write Error| DLQ3
    
    CW1 --> METRICS
    CW2 --> METRICS
    CW3 --> METRICS
    CW4 --> METRICS
    
    ST1 --> LOGS
    ST2 --> LOGS
    ST3 --> LOGS
    
    %% Styling
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef ray fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef daft fill:#96ceb4,stroke:#333,stroke-width:2px,color:#fff
    classDef iceberg fill:#feca57,stroke:#333,stroke-width:2px,color:#000
    classDef error fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#000
    classDef api fill:#54a0ff,stroke:#333,stroke-width:2px,color:#fff
    classDef storage fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef monitor fill:#26de81,stroke:#333,stroke-width:2px,color:#000
    
    class K1,K2,K3 kafka
    class HEAD,NODE1,NODE2,NODE3,CW1,CW2,CW3,CW4 ray
    class KC1,KC2,KC3,ST1,ST2,ST3,T1,T2,T3,DTO1,DTO2,DTO3 processing
    class SW1,SW2,SW3,B1,B2,B3,DF1,DF2,DF3,DT1,DT2,DT3 daft
    class CAT,IT1,IT2,IT3 iceberg
    class DLQ1,DLQ2,DLQ3 error
    class API,MGMT,AUTH api
    class S3 storage
    class METRICS,LOGS monitor
```

## Data Transformation Flow - Structure Changes
```mermaid
graph LR
    %% Raw Kafka Message
    subgraph "Kafka Message"
        KM["📨 ConsumerRecord<br/>{<br/>  key: 'user_123',<br/>  value: '{\"user_id\": 123, \"action\": \"login\", \"timestamp\": \"2024-01-15T10:30:00Z\"}',<br/>  topic: 'user_events',<br/>  partition: 2,<br/>  offset: 45678<br/>}"]
    end
    
    %% JSON Parsing
    subgraph "JSON Parsing"
        JP["🔧 json.loads()<br/>{<br/>  \"user_id\": 123,<br/>  \"action\": \"login\",<br/>  \"timestamp\": \"2024-01-15T10:30:00Z\"<br/>}"]
    end
    
    %% Data Enrichment
    subgraph "Data Enrichment"
        DE["✨ Metadata Addition<br/>{<br/>  \"user_id\": 123,<br/>  \"action\": \"login\",<br/>  \"timestamp\": \"2024-01-15T10:30:00Z\",<br/>  \"_kafka_offset\": 45678,<br/>  \"_kafka_partition\": 2,<br/>  \"_kafka_timestamp\": 1705315800000,<br/>  \"_processing_time\": \"2024-01-15T10:31:05Z\"<br/>}"]
    end
    
    %% SinkRecordDTO
    subgraph "DTO Creation"
        DTO["📦 SinkRecordDTO<br/>{<br/>  key: 'user_123',<br/>  message: {enriched_data},<br/>  topic: 'user_events',<br/>  partition: 2,<br/>  offset: 45678,<br/>  sink_operation: {<br/>    sink_operation_type: UPSERT<br/>  }<br/>}"]
    end
    
    %% Batching
    subgraph "Batching (1000 records)"
        BATCH["📚 Batch Collection<br/>[<br/>  {message1}, {message2}, ..., {message1000}<br/>]<br/><br/>⏱️ Triggers:<br/>• Size >= 1000 records<br/>• Time >= 30 seconds"]
    end
    
    %% Daft DataFrame
    subgraph "Daft DataFrame"
        DF["🗂️ daft.from_pylist()<br/>┌─────────┬────────┬─────────────────────┬──────────────┐<br/>│ user_id │ action │ timestamp           │ _kafka_offset│<br/>├─────────┼────────┼─────────────────────┼──────────────┤<br/>│ 123     │ login  │ 2024-01-15T10:30:00Z│ 45678        │<br/>│ 124     │ logout │ 2024-01-15T10:31:00Z│ 45679        │<br/>│ 125     │ view   │ 2024-01-15T10:32:00Z│ 45680        │<br/>│ ...     │ ...    │ ...                 │ ...          │<br/>└─────────┴────────┴─────────────────────┴──────────────┘"]
    end
    
    %% Daft Transformations
    subgraph "Daft Transformations"
        DT["🔄 Column Operations<br/>df.with_column('year', df['timestamp'].dt.year())<br/>df.with_column('month', df['timestamp'].dt.month())<br/>df.with_column('day', df['timestamp'].dt.day())<br/>df.filter(df['action'] != 'bot_action')<br/>df.select(['user_id', 'action', 'timestamp', 'year', 'month'])"]
    end
    
    %% Final DataFrame
    subgraph "Transformed DataFrame"
        TDF["📊 Final DataFrame<br/>┌─────────┬────────┬─────────────────────┬──────┬───────┐<br/>│ user_id │ action │ timestamp           │ year │ month │<br/>├─────────┼────────┼─────────────────────┼──────┼───────┤<br/>│ 123     │ login  │ 2024-01-15T10:30:00Z│ 2024 │ 1     │<br/>│ 124     │ logout │ 2024-01-15T10:31:00Z│ 2024 │ 1     │<br/>│ 125     │ view   │ 2024-01-15T10:32:00Z│ 2024 │ 1     │<br/>│ ...     │ ...    │ ...                 │ ...  │ ...   │<br/>└─────────┴────────┴─────────────────────┴──────┴───────┘"]
    end
    
    %% Iceberg Write
    subgraph "Iceberg Storage"
        IW["💾 df.write_iceberg()<br/>📁 Table: events.user_events<br/>🗂️ Partitioned by: year=2024/month=01/<br/>📄 Format: Parquet (ZSTD compressed)<br/>🔒 ACID Transaction: Committed<br/>📈 Schema Evolution: Supported"]
    end
    
    %% Physical Storage
    subgraph "Physical Storage"
        PS["🗄️ S3/MinIO Storage<br/>📂 /iceberg/warehouse/events/user_events/<br/>    └── year=2024/<br/>        └── month=01/<br/>            ├── data-001.parquet<br/>            ├── data-002.parquet<br/>            └── metadata/<br/>                ├── manifest-list.avro<br/>                └── manifest.avro"]
    end
    
    %% Error Handling Branch
    subgraph "Error Handling"
        ERR["❌ Transform/Write Failure<br/>📨 Dead Letter Queue<br/>{<br/>  key: 'user_123',<br/>  message: 'original_message',<br/>  topic: 'user_events',<br/>  partition: 2,<br/>  failed_at: 'TRANSFORM',<br/>  error: 'JSON parse error',<br/>  offset: 45678<br/>}"]
    end
    
    %% Flow connections
    KM --> JP
    JP --> DE
    DE --> DTO
    DTO --> BATCH
    BATCH --> DF
    DF --> DT
    DT --> TDF
    TDF --> IW
    IW --> PS
    
    %% Error flows
    JP -.->|Parse Error| ERR
    DE -.->|Transform Error| ERR
    IW -.->|Write Error| ERR
    
    %% Styling
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef daft fill:#96ceb4,stroke:#333,stroke-width:2px,color:#fff
    classDef iceberg fill:#feca57,stroke:#333,stroke-width:2px,color:#000
    classDef storage fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef error fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#000
    
    class KM kafka
    class JP,DE,DTO,BATCH processing
    class DF,DT,TDF daft
    class IW iceberg
    class PS storage
    class ERR error
```

## Architecture Overview

```mermaid
graph TB
    %% External Data Source
    subgraph "Data Source"
        K1[Kafka Topic 1<br/>user_events]
        K2[Kafka Topic 2<br/>transaction_logs]
        K3[Kafka Topic N<br/>system_metrics]
    end
    
    %% Management Layer
    subgraph "Management & Control Layer"
        API[FastAPI Application<br/>event_consumer_app.py<br/>Port: 8000]
        MGMT[Management APIs<br/>POST /manager/start-consumers<br/>POST /manager/stop-consumers<br/>GET /manager/fetch-consumers]
    end
    
    %% Ray Cluster
    subgraph "Ray Distributed Computing Cluster"
        HEAD[Ray Head Node<br/>ray://localhost:10001]
        
        subgraph "Consumer Workers (Ray Actors)"
            CW1["@ray.remote<br/>ConsumerWorker-1<br/>Group: user_events"]
            CW2["@ray.remote<br/>ConsumerWorker-2<br/>Group: user_events"]
            CW3["@ray.remote<br/>ConsumerWorker-3<br/>Group: transaction_logs"]
        end
    end
    
    %% Processing Pipeline
    subgraph "Data Processing Pipeline"
        subgraph "Kafka Consumer Layer"
            KC1[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20]
            KC2[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20]
            KC3[KafkaConsumer<br/>Poll: 1000ms<br/>Max Records: 20]
        end
        
        subgraph "Data Transformation"
            T1[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add Kafka Metadata]
            T2[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add Kafka Metadata]
            T3[DaftIcebergTransformer<br/>JSON Parse + Enrich<br/>Add Kafka Metadata]
        end
    end
    
    %% Batching and Daft Processing
    subgraph "Batching & Columnar Processing"
        subgraph "Stream Writers"
            SW1[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s]
            SW2[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s]
            SW3[BatchedDaftIcebergWriter<br/>Batch Size: 1000<br/>Timeout: 30s]
        end
        
        subgraph "Daft DataFrames"
            DF1[daft.from_pylist<br/>DataFrame Creation]
            DF2[daft.from_pylist<br/>DataFrame Creation]
            DF3[daft.from_pylist<br/>DataFrame Creation]
        end
    end
    
    %% Iceberg Storage Layer
    subgraph "Iceberg Data Lake"
        CAT[Iceberg Catalog<br/>Hive Metastore]
        
        subgraph "Iceberg Tables"
            IT1[events.user_events<br/>Partitioned by: year, month]
            IT2[events.transaction_logs<br/>Partitioned by: year, month]
            IT3[events.system_metrics<br/>Partitioned by: year, month]
        end
        
        S3[AWS S3 / MinIO<br/>Parquet Files]
    end
    
    %% Error Handling
    subgraph "Error Handling"
        DLQ1[DLQ: user_events_dlq]
        DLQ2[DLQ: transaction_logs_dlq]
        DLQ3[DLQ: system_metrics_dlq]
    end
    
    %% Data Flow Connections
    K1 --> CW1
    K1 --> CW2
    K2 --> CW3
    K3 --> CW3
    
    API --> HEAD
    HEAD --> CW1
    HEAD --> CW2
    HEAD --> CW3
    
    CW1 --> KC1
    CW2 --> KC2
    CW3 --> KC3
    
    KC1 --> T1
    KC2 --> T2
    KC3 --> T3
    
    T1 --> SW1
    T2 --> SW2
    T3 --> SW3
    
    SW1 --> DF1
    SW2 --> DF2
    SW3 --> DF3
    
    DF1 --> CAT
    DF2 --> CAT
    DF3 --> CAT
    
    CAT --> IT1
    CAT --> IT2
    CAT --> IT3
    
    IT1 --> S3
    IT2 --> S3
    IT3 --> S3
    
    %% Error flows
    T1 -.-> DLQ1
    T2 -.-> DLQ2
    T3 -.-> DLQ3
```

## Data Transformation Sequence

```mermaid
sequenceDiagram
    participant K as Kafka Topic
    participant CW as ConsumerWorker
    participant T as DaftIcebergTransformer
    participant SW as BatchedDaftIcebergWriter
    participant D as Daft DataFrame
    participant I as Iceberg Table

    K->>CW: ConsumerRecord
    CW->>T: transform(consumer_record)
    T->>T: json.loads() + enrich metadata
    T->>CW: SinkRecordDTO
    CW->>SW: write([sink_record_dto])
    SW->>SW: batch.extend(records)
    
    alt Batch Ready (1000 records OR 30s timeout)
        SW->>D: daft.from_pylist(batch)
        D->>D: add partitioning columns
        D->>D: filter & select
        D->>I: write_iceberg(mode="append")
        I->>SW: write confirmation
        SW->>SW: clear batch
    end
```

## Method 2: Using PlantUML (Alternative)

```plantuml
@startuml
!define RECTANGLE class

RECTANGLE "Kafka Topics" as kafka {
  + user_events
  + transaction_logs
  + system_metrics
}

RECTANGLE "Ray Cluster" as ray {
  + Head Node
  + Worker Nodes
  + Consumer Actors
}

RECTANGLE "Processing Pipeline" as processing {
  + KafkaConsumer
  + SinkTask
  + DaftIcebergTransformer
  + BatchedWriter
}

RECTANGLE "Daft Processing" as daft {
  + DataFrame Creation
  + Column Operations
  + Filtering & Selection
}

RECTANGLE "Iceberg Storage" as iceberg {
  + Catalog (Hive)
  + Partitioned Tables
  + ACID Transactions
}

RECTANGLE "Storage Backend" as storage {
  + S3/MinIO
  + Parquet Files
  + Metadata
}

kafka --> ray
ray --> processing
processing --> daft
daft --> iceberg
iceberg --> storage
@enduml
```

## Method 3: ASCII Art Diagrams (Universal Compatibility)

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Kafka Topics  │───▶│   Ray Cluster    │───▶│   Processing    │
│                 │    │                  │    │   Pipeline      │
│ • user_events   │    │ • Head Node      │    │                 │
│ • transactions  │    │ • Worker Nodes   │    │ • KafkaConsumer │
│ • metrics       │    │ • Consumer Actors│    │ • Transformers  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Storage Backend │◀───│ Iceberg Storage  │◀───│ Daft Processing │
│                 │    │                  │    │                 │
│ • S3/MinIO      │    │ • Catalog        │    │ • DataFrames    │
│ • Parquet Files │    │ • Tables         │    │ • Transformations│
│ • Metadata      │    │ • ACID Txns      │    │ • Batching      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```