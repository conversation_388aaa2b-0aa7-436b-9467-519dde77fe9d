# Distributed Kafka Consumer with Ray, Daft, and Iceberg ELT Pipeline

A comprehensive data processing platform that combines distributed Kafka consumption with modern data lake technologies to implement a medallion architecture (Bronze-Silver-Gold) for ELT processing.

## 🏗️ Architecture Overview

This project extends the original distributed Kafka consumer with:
- **Ray**: Distributed computing for parallel processing
- **Daft**: Fast columnar data processing with Python-native API
- **Iceberg**: ACID-compliant data lake with schema evolution
- **MinIO**: S3-compatible object storage
- **Nessie**: Data catalog with version control and branch management
- **Airflow**: Workflow orchestration for ELT pipelines

### Medallion Architecture (Bronze-Silver-Gold)

1. **Bronze Layer**: Raw data ingestion from Kafka with minimal transformation
2. **Silver Layer**: Cleaned, validated, and standardized data
3. **Gold Layer**: Business-ready analytics and aggregated metrics

## 🚀 Key Features

- **Real-time Data Ingestion**: Kafka to Iceberg with batching and partitioning
- **Distributed Processing**: Ray actors for parallel data processing
- **Data Quality**: Built-in validation and quality checks
- **Schema Evolution**: Automatic schema management with Iceberg
- **Time Travel**: Query historical data with Iceberg snapshots
- **Orchestration**: Airflow DAGs for automated ELT workflows
- **Monitoring**: Comprehensive logging and metrics

## 📋 System Requirements

- **Python**: 3.11+
- **Ray**: 2.48.0+
- **Docker**: 20.10+ (for infrastructure)
- **Docker Compose**: 2.0+
- **Memory**: 8GB+ recommended
- **CPU**: 4+ cores recommended

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd ray-distributed-kafka-consumer-python

# Copy environment configuration
cp .env.example .env

# Install dependencies
pip install -r requirements-dev.txt
pip install -e .
```

### 2. Start Infrastructure Services

```bash
# Start all services (Kafka, MinIO, Nessie, Airflow, PostgreSQL, Redis)
docker-compose up -d

# Wait for services to be ready (check health)
docker-compose ps
```

### 3. Initialize Ray Cluster

```bash
# Start Ray head node
ray start --head --port=6379 --dashboard-host=0.0.0.0

# For distributed setup, start worker nodes:
# ray start --address='ray://localhost:10001'
```

### 4. Configure and Start Kafka Consumer

```bash
# Set environment variables
export WORKER_CONFIG_PATH=config/daft_iceberg_consumer_config.json
export LOCAL_MODE=Y

# Start the consumer application
uvicorn src.event_consumer_app:app --port 8000 --reload
```

## 📁 Configuration Files

### Kafka Consumer Configuration

The project includes two consumer configurations:

1. **Original Configuration**: `config/consumer_config.json` - Basic console output
2. **Daft-Iceberg Configuration**: `config/daft_iceberg_consumer_config.json` - Full ELT pipeline

### Example Daft-Iceberg Configuration

```json
{
  "consumer_name": "user_events_consumer",
  "topic_name": "user-events",
  "number_of_workers": 3,
  "sink_configs": {
    "transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer",
    "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"],
    "batch_size": 1000,
    "batch_timeout_seconds": 30,
    "namespace": "bronze",
    "auto_create_tables": true
  }
}
```

## 🔄 ELT Pipeline Workflow

### Real-time Bronze Layer (Kafka → Iceberg)

1. **Kafka Consumer**: Ray actors consume messages from Kafka topics
2. **Daft Transformation**: Messages are processed using Daft DataFrames
3. **Metadata Enrichment**: Kafka metadata and processing timestamps added
4. **Batching**: Records are batched for efficient writes (1000 records or 30s timeout)
5. **Iceberg Write**: Batches written to partitioned Iceberg tables in MinIO

### Batch Silver Layer (Bronze → Silver)

1. **Airflow Scheduler**: Triggers daily processing at 2 AM
2. **Data Quality**: Validation, deduplication, and cleaning
3. **Schema Standardization**: Consistent data types and formats
4. **Ray Processing**: Distributed processing across multiple workers
5. **Silver Tables**: Clean, validated data ready for analytics

### Batch Gold Layer (Silver → Gold)

1. **Airflow Scheduler**: Triggers daily processing at 4 AM
2. **Business Logic**: Aggregations and KPI calculations
3. **Analytics Creation**: User behavior, transaction metrics, system health
4. **Ray Processing**: Distributed analytics processing
5. **Gold Tables**: Business-ready datasets for dashboards and reports

# How data flows:

## Kafka to Iceberg Data Flow with Ray and Daft

```mermaid
  graph TB
    %% Data Sources
    subgraph "Data Sources"

        K1[Kafka Topic 1<br/>user_events]
        K2[Kafka Topic 2<br/>transaction_logs]
        K3[Kafka Topic N<br/>system_metrics]
    
    end
    
    %% Ray Cluster Processing
    subgraph "Ray Cluster - Real-time Processing"

        direction TB
        CW1[Ray Actor<br/>ConsumerWorker-1]
        CW2[Ray Actor<br/>ConsumerWorker-2]
        CW3[Ray Actor<br/>ConsumerWorker-N]
        
        subgraph "New Components"
            DIT["DaftIcebergTransformer<br/>• JSON parsing<br/>• Daft DataFrame creation<br/>• Metadata enrichment"]
            DISW["BatchedDaftIcebergWriter<br/>• Batch accumulation (1000 records)<br/>• Daft transformations<br/>• Iceberg writes"]
    
        end
    
    end
    
    %% Infrastructure Layer
    subgraph "Infrastructure Services"

        direction LR
        MINIO[MinIO<br/>S3-compatible storage<br/>Data lake backend]
        NESSIE[Nessie<br/>Data catalog<br/>Version control<br/>Branch management]
        KAFKA[Kafka Cluster<br/>Message streaming]
    
    end
    
    %% Iceberg Data Lake - Bronze Layer
    subgraph "Bronze Layer (Raw Data)"

        direction TB
        IB1[Iceberg Table<br/>bronze.user_events<br/>Partitioned: year/month/day]
        IB2[Iceberg Table<br/>bronze.transaction_logs<br/>Partitioned: year/month/day]
        IB3[Iceberg Table<br/>bronze.system_metrics<br/>Partitioned: year/month/day]
    
    end
    
    %% Airflow Orchestration
    subgraph "Airflow Orchestration"

        direction TB
        DAG1[Bronze→Silver DAG<br/>• Data validation<br/>• Schema standardization<br/>• Quality checks]
        DAG2[Silver→Gold DAG<br/>• Business logic<br/>• Aggregations<br/>• Analytics prep]
        
        subgraph "Ray Tasks"
            RT1[Ray Task<br/>Silver Processing<br/>Daft transformations]
            RT2[Ray Task<br/>Gold Processing<br/>Daft aggregations]
    
        end
    
    end
    
    %% Silver Layer
    subgraph "Silver Layer (Cleaned Data)"

        direction TB
        IS1[Iceberg Table<br/>silver.user_events_clean<br/>• Validated data<br/>• Standardized schema]
        IS2[Iceberg Table<br/>silver.transaction_logs_clean<br/>• Deduplication<br/>• Data quality rules]
        IS3[Iceberg Table<br/>silver.system_metrics_clean<br/>• Normalized metrics<br/>• Outlier handling]
    
    end
    
    %% Gold Layer
    subgraph "Gold Layer (Analytics Ready)"

        direction TB
        IG1[Iceberg Table<br/>gold.user_behavior_summary<br/>• Daily aggregations<br/>• Business KPIs]
        IG2[Iceberg Table<br/>gold.financial_metrics<br/>• Transaction summaries<br/>• Revenue analytics]
        IG3[Iceberg Table<br/>gold.system_health_dashboard<br/>• Performance metrics<br/>• Alert thresholds]
    
    end
    
    %% Data Flow - Real-time (Bronze)

    K1 --> CW1
    K2 --> CW2
    K3 --> CW3
    
    CW1 --> DIT
    CW2 --> DIT
    CW3 --> DIT
    
    DIT --> DISW
    DISW --> NESSIE
    NESSIE --> IB1
    NESSIE --> IB2
    NESSIE --> IB3
    


    IB1 --> MINIO
    IB2 --> MINIO
    IB3 --> MINIO
    


    %% Data Flow - Batch Processing (Silver/Gold)

    DAG1 --> RT1
    RT1 --> IS1
    RT1 --> IS2
    RT1 --> IS3
    


    DAG2 --> RT2
    RT2 --> IG1
    RT2 --> IG2
    RT2 --> IG3
    

    IB1 -.-> DAG1
    IB2 -.-> DAG1
    IB3 -.-> DAG1
    

    IS1 -.-> DAG2
    IS2 -.-> DAG2
    IS3 -.-> DAG2
    

    IS1 --> MINIO
    IS2 --> MINIO
    IS3 --> MINIO
    IG1 --> MINIO
    IG2 --> MINIO
    IG3 --> MINIO
    

    %% Styling
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef ray fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef infra fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef bronze fill:#cd7f32,stroke:#333,stroke-width:2px,color:#fff
    classDef silver fill:#c0c0c0,stroke:#333,stroke-width:2px,color:#000
    classDef gold fill:#ffd700,stroke:#333,stroke-width:2px,color:#000
    classDef airflow fill:#017cee,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#96ceb4,stroke:#333,stroke-width:2px,color:#000
    

    class K1,K2,K3,KAFKA kafka
    class CW1,CW2,CW3,RT1,RT2 ray
    class MINIO,NESSIE infra
    class IB1,IB2,IB3 bronze
    class IS1,IS2,IS3 silver
    class IG1,IG2,IG3 gold
    class DAG1,DAG2 airflow
    class DIT,DISW processing
```

## Complete CDC Data Flow: MySQL->Kafka->Ray->Daft->Iceberg->Minio
```mermaid
graph TB
    %% MySQL Source
    subgraph "MySQL Database"
        MYSQL[(MySQL Database<br/>BW_DATA.customers)]
        CDC_EVENT[CDC Event<br/>INSERT/UPDATE/DELETE<br/>Customer Record]
    end
    
    %% Debezium CDC
    subgraph "Debezium CDC Connector"
        DEBEZIUM[Debezium MySQL Connector<br/>dev-iops-operations-src-mysql<br/>• Captures binlog changes<br/>• Transforms to Avro<br/>• Adds CDC metadata]
    end
    
    %% Kafka Infrastructure
    subgraph "Kafka Cluster"
        KAFKA_TOPIC[Topic: inventory.customers<br/>• Avro serialized messages<br/>• CDC metadata included<br/>• Partitioned for scalability]
        SCHEMA_REG[Schema Registry<br/>• Avro schema management<br/>• Schema evolution support]
    end
    
    %% FastAPI Application
    subgraph "FastAPI Management Layer"
        FASTAPI[FastAPI App<br/>event_consumer_app.py<br/>Port: 8000]
        STARTUP["Startup Event<br/>cwm.start_all_workers()"]
    end
    
    %% Consumer Manager
    subgraph "Consumer Worker Manager"
        CWM[ConsumerWorkerManager<br/>consumer_manager.py]
        CONFIG[Configuration Loading<br/>daft_iceberg_consumer_config.json<br/>• Topic: inventory.customers<br/>• Workers: 3<br/>• Transformer: DaftIcebergTransformer<br/>• Writer: BatchedDaftIcebergWriter]
    end
    
    %% Ray Cluster
    subgraph "Ray Distributed Computing"
        direction TB
        RAY_HEAD[Ray Head Node<br/>ray://localhost:10001]
        
        subgraph "Ray Consumer Workers"
            CW1[Ray Actor<br/>ConsumerWorker-1<br/>@ray.remote<br/>CPU: 0.25]
            CW2[Ray Actor<br/>ConsumerWorker-2<br/>@ray.remote<br/>CPU: 0.25]
            CW3[Ray Actor<br/>ConsumerWorker-3<br/>@ray.remote<br/>CPU: 0.25]
        end
    end
    
    %% Kafka Consumer Layer
    subgraph "Kafka Consumer Layer"
        KC1[KafkaConsumer-1<br/>• Group: customers_consumer<br/>• Poll: 1000ms<br/>• Max Records: 50<br/>• Avro Deserializer]
        KC2[KafkaConsumer-2<br/>• Group: customers_consumer<br/>• Poll: 1000ms<br/>• Max Records: 50<br/>• Avro Deserializer]
        KC3[KafkaConsumer-3<br/>• Group: customers_consumer<br/>• Poll: 1000ms<br/>• Max Records: 50<br/>• Avro Deserializer]
    end
    
    %% Processing Pipeline
    subgraph "Message Processing Pipeline"
        direction TB
        
        subgraph "Sink Task Processing"
            ST1[SinkTask.process<br/>sink_task.py<br/>• Rate limit: 20 calls/sec<br/>• Error handling<br/>• DLQ support]
            ST2[SinkTask.process<br/>sink_task.py<br/>• Rate limit: 20 calls/sec<br/>• Error handling<br/>• DLQ support]
            ST3[SinkTask.process<br/>sink_task.py<br/>• Rate limit: 20 calls/sec<br/>• Error handling<br/>• DLQ support]
        end
        
        subgraph "Data Transformation"
            DIT1[DaftIcebergTransformer<br/>daft_iceberg_transformer.py<br/>• Parse Avro CDC data<br/>• Enrich with Kafka metadata<br/>• Add partitioning columns<br/>• Create SinkRecordDTO]
            DIT2[DaftIcebergTransformer<br/>daft_iceberg_transformer.py<br/>• Parse Avro CDC data<br/>• Enrich with Kafka metadata<br/>• Add partitioning columns<br/>• Create SinkRecordDTO]
            DIT3[DaftIcebergTransformer<br/>daft_iceberg_transformer.py<br/>• Parse Avro CDC data<br/>• Enrich with Kafka metadata<br/>• Add partitioning columns<br/>• Create SinkRecordDTO]
        end
    end
    
    %% Batching and Daft Processing
    subgraph "Batching & Columnar Processing"
        direction TB
        
        subgraph "Batched Writers"
            BDW1[BatchedDaftIcebergWriter<br/>daft_iceberg_writer.py<br/>• Batch Size: 1000 records<br/>• Timeout: 30 seconds<br/>• Thread-safe batching<br/>• Auto table creation]
            BDW2[BatchedDaftIcebergWriter<br/>daft_iceberg_writer.py<br/>• Batch Size: 1000 records<br/>• Timeout: 30 seconds<br/>• Thread-safe batching<br/>• Auto table creation]
            BDW3[BatchedDaftIcebergWriter<br/>daft_iceberg_writer.py<br/>• Batch Size: 1000 records<br/>• Timeout: 30 seconds<br/>• Thread-safe batching<br/>• Auto table creation]
        end
        
        subgraph "Batch Decision Logic"
            BATCH_CHECK{Batch Ready?<br/>Size >= 1000 OR<br/>Time >= 30s}
        end
        
        subgraph "Daft DataFrame Operations"
            DF_CREATE[daft.from_pylist<br/>• Convert CDC records to DataFrame<br/>• Columnar format optimization<br/>• Type casting and validation]
            DF_TRANSFORM[DataFrame Transformations<br/>• Cast partition columns to int64<br/>• Handle timestamp formatting<br/>• Prepare for Iceberg schema]
        end
    end
    
    %% Iceberg Infrastructure
    subgraph "Iceberg Data Lake Infrastructure"
        direction TB
        
        subgraph "Catalog Management"
            NESSIE[Nessie Catalog<br/>localhost:19120<br/>• ACID transactions<br/>• Schema evolution<br/>• Branch management<br/>• Commit history]
            ICM[IcebergCatalogManager<br/>iceberg_utils.py<br/>• Table creation/management<br/>• Schema validation<br/>• Partition management]
        end
        
        subgraph "Table Operations"
            TABLE_CHECK{Table Exists?<br/>inventory_customers}
            TABLE_CREATE[Create Bronze Table<br/>• Schema: Standard bronze<br/>• Partitions: year/month/day<br/>• Properties: Parquet, ZSTD<br/>• Metadata retention: 5 versions]
            TABLE_LOAD[Load Existing Table<br/>• Get table reference<br/>• Validate schema<br/>• Check partitions]
        end
    end
    
    %% Storage Layer
    subgraph "MinIO S3-Compatible Storage"
        direction TB
        MINIO[MinIO Server<br/>localhost:9000<br/>• Bucket: data-lake<br/>• S3-compatible API<br/>• High availability<br/>• Data durability]
        
        subgraph "Data Organization"
            BRONZE_PATH[Bronze Layer Path<br/>s3a://data-lake/warehouse/<br/>bronze/inventory_customers/<br/>year=2024/month=01/day=15/<br/>• Parquet files<br/>• ZSTD compression<br/>• Partitioned storage]
        end
    end
    
    %% Error Handling
    subgraph "Error Handling & Monitoring"
        DLQ[Dead Letter Queue<br/>inventory-customers-dlq<br/>• Failed transformations<br/>• Failed writes<br/>• Retry logic]
        
        MONITORING[Monitoring & Observability<br/>• Ray Dashboard: localhost:8265<br/>• MinIO Console: localhost:9001<br/>• Nessie UI: localhost:19120<br/>• FastAPI Health: localhost:8000]
    end
    
    %% Data Flow Connections - Source to Kafka
    MYSQL --> CDC_EVENT
    CDC_EVENT --> DEBEZIUM
    DEBEZIUM --> KAFKA_TOPIC
    DEBEZIUM --> SCHEMA_REG
    SCHEMA_REG -.-> KAFKA_TOPIC
    
    %% FastAPI to Ray
    FASTAPI --> STARTUP
    STARTUP --> CWM
    CWM --> CONFIG
    CONFIG --> RAY_HEAD
    
    %% Ray Worker Creation
    RAY_HEAD --> CW1
    RAY_HEAD --> CW2
    RAY_HEAD --> CW3
    
    %% Kafka Consumption
    KAFKA_TOPIC --> KC1
    KAFKA_TOPIC --> KC2
    KAFKA_TOPIC --> KC3
    
    CW1 --> KC1
    CW2 --> KC2
    CW3 --> KC3
    
    %% Processing Pipeline
    KC1 --> ST1
    KC2 --> ST2
    KC3 --> ST3
    
    ST1 --> DIT1
    ST2 --> DIT2
    ST3 --> DIT3
    
    %% Batching Flow
    DIT1 --> BDW1
    DIT2 --> BDW2
    DIT3 --> BDW3
    
    BDW1 --> BATCH_CHECK
    BDW2 --> BATCH_CHECK
    BDW3 --> BATCH_CHECK
    
    BATCH_CHECK -->|Yes| DF_CREATE
    BATCH_CHECK -->|No| BDW1
    
    DF_CREATE --> DF_TRANSFORM
    
    %% Iceberg Operations
    DF_TRANSFORM --> ICM
    ICM --> NESSIE
    ICM --> TABLE_CHECK
    
    TABLE_CHECK -->|No| TABLE_CREATE
    TABLE_CHECK -->|Yes| TABLE_LOAD
    
    TABLE_CREATE --> NESSIE
    TABLE_LOAD --> NESSIE
    
    %% Final Write
    NESSIE --> MINIO
    MINIO --> BRONZE_PATH
    
    %% Error Flows
    ST1 -.->|Transform Error| DLQ
    ST2 -.->|Transform Error| DLQ
    ST3 -.->|Transform Error| DLQ
    
    BDW1 -.->|Write Error| DLQ
    BDW2 -.->|Write Error| DLQ
    BDW3 -.->|Write Error| DLQ
    
    %% Monitoring Connections
    CW1 -.-> MONITORING
    CW2 -.-> MONITORING
    CW3 -.-> MONITORING
    NESSIE -.-> MONITORING
    MINIO -.-> MONITORING
    
    %% Commit Flow (Success Path)
    BRONZE_PATH -.->|Success| KC1
    BRONZE_PATH -.->|Success| KC2
    BRONZE_PATH -.->|Success| KC3
    
    %% Styling
    classDef mysql fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef ray fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef daft fill:#96ceb4,stroke:#333,stroke-width:2px,color:#000
    classDef iceberg fill:#feca57,stroke:#333,stroke-width:2px,color:#000
    classDef storage fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef error fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#000
    classDef api fill:#54a0ff,stroke:#333,stroke-width:2px,color:#fff
    classDef monitor fill:#26de81,stroke:#333,stroke-width:2px,color:#000
    
    class MYSQL,CDC_EVENT mysql
    class DEBEZIUM,KAFKA_TOPIC,SCHEMA_REG kafka
    class RAY_HEAD,CW1,CW2,CW3 ray
    class KC1,KC2,KC3,ST1,ST2,ST3,DIT1,DIT2,DIT3 processing
    class BDW1,BDW2,BDW3,BATCH_CHECK,DF_CREATE,DF_TRANSFORM daft
    class NESSIE,ICM,TABLE_CHECK,TABLE_CREATE,TABLE_LOAD iceberg
    class MINIO,BRONZE_PATH storage
    class DLQ error
    class FASTAPI,STARTUP,CWM,CONFIG api
    class MONITORING monitor
```

## Detailed Method Call Sequence: CDC Data Processing Pipeline
```mermaid
sequenceDiagram
    participant MySQL as MySQL Database<br/>BW_DATA.customers
    participant Debezium as Debezium Connector<br/>dev-iops-operations-src-mysql
    participant Kafka as Kafka Topic<br/>inventory.customers
    participant SchemaRegistry as Schema Registry<br/>localhost:8081
    participant FastAPI as FastAPI App<br/>event_consumer_app.py
    participant CWM as ConsumerWorkerManager<br/>consumer_manager.py
    participant Ray as Ray Actor<br/>ConsumerWorker
    participant KC as KafkaConsumer<br/>kafka-python
    participant ST as SinkTask<br/>sink_task.py
    participant DIT as DaftIcebergTransformer<br/>daft_iceberg_transformer.py
    participant BDW as BatchedDaftIcebergWriter<br/>daft_iceberg_writer.py
    participant ICM as IcebergCatalogManager<br/>iceberg_utils.py
    participant Nessie as Nessie Catalog<br/>localhost:19120
    participant MinIO as MinIO Storage<br/>localhost:9000
    participant Daft as Daft DataFrame<br/>daft.from_pylist()
    participant DLQ_Writer as DLQ Writer<br/>Dead Letter Queue

    Note over MySQL,MinIO: Application Startup & Initialization
    FastAPI->>CWM: @app.on_event("startup")<br/>cwm.start_all_workers()
    
    Note over CWM: Configuration Loading Details
    CWM->>CWM: ConfigManager.load_config(WORKER_CONFIG_PATH)
    CWM->>CWM: Parse consumer configurations for each topic
    CWM->>CWM: Validate transformer and writer class paths
    
    loop For each worker in config (e.g., 3 workers)
        CWM->>Ray: ConsumerWorker.options(name=f"{consumer_name}-{i}").remote(config, worker_name)
        Ray->>Ray: __init__(config, worker_name)
        CWM->>Ray: worker_actor.run.remote()
    end
    
    Note over Ray,ST: Consumer Worker Initialization
    Ray->>KC: KafkaConsumer(<br/>bootstrap_servers=config.bootstrap_servers,<br/>group_id=config.consumer_name,<br/>key_deserializer=AvroDeserializer,<br/>value_deserializer=AvroDeserializer,<br/>auto_offset_reset=config.auto_offset_reset)
    KC->>SchemaRegistry: Register Avro deserializers
    SchemaRegistry-->>KC: Schema validation and deserialization setup
    Ray->>KC: consumer.subscribe([topic_name])
    Note over KC: topic_name from config (e.g., "inventory.customers")
    Ray->>ST: SinkTask(config)
    ST->>DIT: get_transformer(transformer_cls_path, sink_configs)
    DIT->>DIT: __init__(config)<br/>• add_kafka_metadata=True<br/>• parse_json_value=True
    ST->>BDW: get_stream_writers(stream_writer_cls_paths, sink_configs)
    BDW->>BDW: __init__(config)<br/>• batch_size=1000<br/>• batch_timeout_seconds=30
    BDW->>ICM: get_iceberg_catalog_manager()
    ICM->>Nessie: load_catalog('nessie_catalog', **catalog_config)
    ICM->>MinIO: Minio(endpoint, access_key, secret_key)
    
    Note over MySQL,Kafka: CDC Event Capture & Publishing
    MySQL->>MySQL: Customer record change<br/>(INSERT/UPDATE/DELETE)
    MySQL->>Debezium: Binlog event captured
    Debezium->>Debezium: Transform to Avro format<br/>Add CDC metadata (__op, __source_ts_ms, etc.)
    Debezium->>Kafka: Publish to inventory.customers topic
    
    Note over Ray,MinIO: Main Processing Loop (Continuous)
    loop Every 1000ms poll cycle
        Ray->>KC: consumer.poll(timeout_ms=1000)
        KC->>Kafka: Fetch CDC messages
        Kafka-->>KC: List[ConsumerRecord] with Avro CDC data
        KC-->>Ray: tp_records_dict
        
        alt Records available
            Ray->>ST: sink_task.process(consumer_records)
            
            loop For each CDC ConsumerRecord
                ST->>DIT: stream_transformer.transform(consumer_record)
                
                Note over DIT: Data Transformation Process
                DIT->>DIT: _parse_message_value(consumer_record.value)<br/>Parse Avro CDC data
                DIT->>DIT: _enrich_with_metadata(message_data, consumer_record)<br/>Add Kafka metadata & partitioning columns
                DIT->>DIT: Create SinkRecordDTO(key, enriched_message, metadata)
                DIT-->>ST: SinkRecordDTO with CDC data + metadata
                
                alt Transform Success
                    ST->>BDW: write_to_sink([sink_record_dto])
                    BDW->>BDW: current_batch.extend(streams)<br/>Thread-safe batch accumulation
                    
                    alt Batch Ready
                        Note over BDW: Conditions:<br/>• current_batch.size >= batch_size (1000)<br/>• OR (current_time - last_write_time) >= batch_timeout_seconds (30s)
                        BDW->>BDW: _should_flush_batch() returns True
                        BDW->>BDW: _flush_batch()<br/>Process accumulated CDC records
                        BDW->>BDW: _group_records_by_topic(batch_to_process)<br/>Group by inventory.customers
                        BDW->>BDW: _write_topic_batch(topic, records)
                        
                        Note over BDW,ICM: Table Management
                        BDW->>BDW: _get_or_create_table("inventory_customers")
                        BDW->>ICM: get_table("inventory_customers", "bronze")
                        
                        alt Table doesn't exist
                            ICM->>ICM: create_bronze_table("inventory_customers", "bronze")
                            ICM->>Nessie: create_namespace("bronze")
                            ICM->>Nessie: create_table(identifier, schema, partition_spec, properties)
                            Nessie-->>ICM: Table reference
                        else Table exists
                            ICM->>Nessie: load_table("bronze.inventory_customers")
                            Nessie-->>ICM: Existing table reference
                        end
                        
                        Note over BDW,Daft: Daft DataFrame Processing
                        BDW->>BDW: _sink_record_to_dict(record) for each CDC record<br/>Convert to dict format
                        BDW->>Daft: daft.from_pylist(record_dicts)<br/>Create DataFrame from CDC data
                        Daft-->>BDW: DataFrame with CDC records
                        BDW->>BDW: _prepare_dataframe_for_iceberg(df)<br/>Cast types, handle timestamps
                        BDW->>Daft: df.with_column() operations<br/>Ensure proper data types
                        Daft-->>BDW: Prepared DataFrame
                        
                        Note over BDW,MinIO: Iceberg Write Operation with Retry Logic
                        BDW->>BDW: _write_dataframe_to_iceberg(df, table, topic)

                        loop Retry up to max_retries (3 attempts)
                            BDW->>Daft: df.write_iceberg(table, mode="append")
                            Daft->>Nessie: Begin ACID transaction
                            Daft->>MinIO: Write Parquet files to<br/>s3a://data-lake/warehouse/bronze/inventory_customers/<br/>year=2024/month=01/day=15/

                            alt Write Success
                                MinIO-->>Daft: Write confirmation
                                Daft->>Nessie: Commit transaction<br/>Update table metadata
                                Nessie-->>Daft: Transaction committed
                                Daft-->>BDW: Write success
                                Note over BDW: Exit retry loop on success
                            else Write Failed
                                Note over BDW: Attempt failed, will retry if attempts remaining
                                BDW->>BDW: Wait retry_delay_seconds * (attempt + 1)<br/>Exponential backoff delay
                            end
                        end

                        alt All Retries Failed
                            BDW->>ST: Raise exception for DLQ handling
                        end
                        
                        BDW->>BDW: current_batch = []<br/>last_write_time = time.time()
                    else
                        Note over BDW: Buffer CDC records for next batch
                    end
                    
                else Transform Failed
                    ST->>ST: log.error("Transform failed", exc_info=True)
                    ST->>ST: handle_dlq_push(consumer_record.key, consumer_record.value,<br/>consumer_record.topic, consumer_record.partition,<br/>'TRANSFORM', str(error), consumer_record.offset)
                    ST->>DLQ_Writer: dlq_stream_writer.write([DeadLetterDTO])
                    DLQ_Writer->>Kafka: Publish to inventory-customers-dlq topic
                end
                
                alt Write Failed
                    ST->>ST: handle_dlq_push(key, message, topic, partition, 'SINK_UPDATE', error, offset)
                    ST->>DLQ_Writer: dlq_stream_writer.write([DeadLetterDTO])
                    DLQ_Writer->>Kafka: Publish to inventory-customers-dlq topic
                end
            end
            
            alt All Records Processed Successfully
                Note over Ray,KC: Kafka Offset Commit
                Ray->>KC: consumer.commit()<br/>Commit processed CDC offsets
                KC-->>Ray: Commit confirmation
            else Some Records Failed
                Note over Ray: Skip commit to allow reprocessing
            end
        end
        
        alt Stop Signal Received
            Ray->>KC: consumer.close()
            Ray->>Ray: is_closed = True
            break
        end
    end
    
    Note over FastAPI,Ray: Graceful Shutdown Sequence
    FastAPI->>CWM: POST /manager/stop-consumers
    CWM->>Ray: ray.get(worker_actor.stop_consumer.remote())
    Ray->>Ray: stop_worker = True
    Ray->>KC: consumer.close()
    Ray->>BDW: force_flush()<br/>Flush remaining batches
    BDW->>Daft: Final write_iceberg() if current_batch not empty
    CWM->>Ray: ray.kill(worker_actor)<br/>Only after graceful cleanup
```

## Data Transformation Journey: MySQL Customer Record -> Bronze Iceberg Table
```mermaid
 graph LR
    %% Original MySQL Record
    subgraph "MySQL Database Record"
        MYSQL_REC["📊 customers table<br/>{<br/>  id: 'cust-123',<br/>  plate_number: 'ABC-1234',<br/>  car_make: 'Toyota',<br/>  car_year: 2020,<br/>  owner_name: 'John Doe',<br/>  owner_address: '123 Main St',<br/>  owner_phone_number: '+1234567890',<br/>  subscription_status: 'active',<br/>  subscription_start: '2024-01-01',<br/>  subscription_end: '2024-12-31',<br/>  balance: 150.75,<br/>  timestamp: '2024-01-15 10:30:00'<br/>}"]
    end
    
    %% Debezium CDC Transformation
    subgraph "Debezium CDC Event"
        CDC_REC["🔄 CDC Avro Message<br/>{<br/>  id: 'cust-123',<br/>  plate_number: 'ABC-1234',<br/>  car_make: 'Toyota',<br/>  car_year: 2020,<br/>  owner_name: 'John Doe',<br/>  owner_address: '123 Main St',<br/>  owner_phone_number: '+1234567890',<br/>  subscription_status: 'active',<br/>  subscription_start: '2024-01-01',<br/>  subscription_end: '2024-12-31',<br/>  balance: 150.75,<br/>  timestamp: '2024-01-15T10:30:00Z',<br/>  __op: 'c',<br/>  __source_name: 'BW_DATA',<br/>  __db: 'BW_DATA',<br/>  __table: 'customers',<br/>  __lsn: '12345',<br/>  __source_ts_ms: 1705315800000<br/>}"]
    end
    
    %% Kafka Consumer Record
    subgraph "Kafka ConsumerRecord"
        KAFKA_REC["📨 ConsumerRecord<br/>{<br/>  key: 'cust-123',<br/>  value: {CDC_Avro_Data},<br/>  topic: 'inventory.customers',<br/>  partition: 2,<br/>  offset: 45678,<br/>  timestamp: 1705315800000,<br/>  headers: []<br/>}"]
    end
    
    %% DaftIcebergTransformer Processing
    subgraph "DaftIcebergTransformer"
        PARSED["🔧 _parse_message_value()<br/>{<br/>  id: 'cust-123',<br/>  plate_number: 'ABC-1234',<br/>  car_make: 'Toyota',<br/>  car_year: 2020,<br/>  owner_name: 'John Doe',<br/>  owner_address: '123 Main St',<br/>  owner_phone_number: '+1234567890',<br/>  subscription_status: 'active',<br/>  subscription_start: '2024-01-01',<br/>  subscription_end: '2024-12-31',<br/>  balance: 150.75,<br/>  timestamp: '2024-01-15T10:30:00Z',<br/>  __op: 'c',<br/>  __source_ts_ms: 1705315800000<br/>}"]
        
        ENRICHED["✨ _enrich_with_metadata()<br/>{<br/>  id: 'cust-123',<br/>  plate_number: 'ABC-1234',<br/>  car_make: 'Toyota',<br/>  car_year: 2020,<br/>  owner_name: 'John Doe',<br/>  owner_address: '123 Main St',<br/>  owner_phone_number: '+1234567890',<br/>  subscription_status: 'active',<br/>  subscription_start: '2024-01-01',<br/>  subscription_end: '2024-12-31',<br/>  balance: 150.75,<br/>  timestamp: '2024-01-15T10:30:00Z',<br/>  __op: 'c',<br/>  __source_ts_ms: 1705315800000,<br/>  _kafka_topic: 'inventory.customers',<br/>  _kafka_partition: 2,<br/>  _kafka_offset: 45678,<br/>  _kafka_key: 'cust-123',<br/>  _kafka_timestamp: '2024-01-15T10:30:00Z',<br/>  _processing_time: '2024-01-15T10:31:05Z',<br/>  _year: 2024,<br/>  _month: 1,<br/>  _day: 15<br/>}"]
    end
    
    %% SinkRecordDTO
    subgraph "SinkRecordDTO Creation"
        DTO["📦 SinkRecordDTO<br/>{<br/>  key: 'cust-123',<br/>  message: {enriched_data},<br/>  topic: 'inventory.customers',<br/>  partition: 2,<br/>  offset: 45678,<br/>  sink_operation: {<br/>    sink_operation_type: UPSERT<br/>  }<br/>}"]
    end
    
    %% Batching Process
    subgraph "BatchedDaftIcebergWriter"
        BATCH["📚 Batch Accumulation<br/>current_batch = [<br/>  {customer_record_1},<br/>  {customer_record_2},<br/>  ...,<br/>  {customer_record_1000}<br/>]<br/><br/>⏱️ Flush Triggers:<br/>• Size >= 1000 records<br/>• Time >= 30 seconds"]
        
        DICT_CONV["🔄 _sink_record_to_dict()<br/>{<br/>  key: 'cust-123',<br/>  value: '{\"id\":\"cust-123\",\"plate_number\":\"ABC-1234\",...}',<br/>  topic: 'inventory.customers',<br/>  partition: 2,<br/>  offset: 45678,<br/>  timestamp: '2024-01-15T10:31:05Z',<br/>  kafka_timestamp: '2024-01-15T10:30:00Z',<br/>  processing_time: '2024-01-15T10:31:05Z',<br/>  year: 2024,<br/>  month: 1,<br/>  day: 15<br/>}"]
    end
    
    %% Daft DataFrame
    subgraph "Daft DataFrame Processing"
        DF["🗂️ daft.from_pylist()<br/>┌─────────────┬────────────────────────────────┬─────────────────────┬─────────┬────────┬──────┬───────┬─────┐<br/>│ key         │ value                          │ topic               │ partition│ offset │ year │ month │ day │<br/>├─────────────┼────────────────────────────────┼─────────────────────┼─────────┼────────┼──────┼───────┼─────┤<br/>│ cust-123    │ {\"id\":\"cust-123\",\"plate_nu...} │ inventory.customers │ 2       │ 45678  │ 2024 │ 1     │ 15  │<br/>│ cust-124    │ {\"id\":\"cust-124\",\"plate_nu...} │ inventory.customers │ 2       │ 45679  │ 2024 │ 1     │ 15  │<br/>│ cust-125    │ {\"id\":\"cust-125\",\"plate_nu...} │ inventory.customers │ 2       │ 45680  │ 2024 │ 1     │ 15  │<br/>│ ...         │ ...                            │ ...                 │ ...     │ ...    │ ...  │ ...   │ ... │<br/>└─────────────┴────────────────────────────────┴─────────────────────┴─────────┴────────┴──────┴───────┴─────┘"]
        
        DF_PREP["🔄 _prepare_dataframe_for_iceberg()<br/>• Cast partition to int64<br/>• Cast offset to int64<br/>• Cast year to int64<br/>• Cast month to int64<br/>• Cast day to int64<br/>• Validate timestamp formats"]
    end
    
    %% Iceberg Table Schema
    subgraph "Bronze Iceberg Table Schema"
        SCHEMA["📋 Bronze Table Schema<br/>bronze.inventory_customers<br/><br/>Columns:<br/>• key: STRING (nullable)<br/>• value: STRING (required)<br/>• topic: STRING (required)<br/>• partition: LONG (required)<br/>• offset: LONG (required)<br/>• timestamp: TIMESTAMP (required)<br/>• kafka_timestamp: TIMESTAMP (required)<br/>• processing_time: TIMESTAMP (required)<br/>• year: LONG (required)<br/>• month: LONG (required)<br/>• day: LONG (required)<br/><br/>Partitioning:<br/>• PARTITION BY year, month, day<br/>• Transform: YearTransform, MonthTransform, DayTransform"]
    end
    
    %% Final Iceberg Write
    subgraph "Iceberg Write Operation"
        WRITE["💾 df.write_iceberg(table, mode='append')<br/>📁 Storage Path:<br/>s3a://data-lake/warehouse/bronze/inventory_customers/<br/>    └── year=2024/<br/>        └── month=01/<br/>            └── day=15/<br/>                ├── data-001.parquet<br/>                ├── data-002.parquet<br/>                └── ...<br/><br/>📄 Format: Parquet (ZSTD compressed)<br/>🔒 ACID Transaction: Committed via Nessie<br/>📈 Schema Evolution: Supported<br/>🕐 Snapshot: Created with metadata"]
    end
    
    %% Physical Storage
    subgraph "MinIO Physical Storage"
        STORAGE["🗄️ MinIO Object Storage<br/>Bucket: data-lake<br/>Objects:<br/>• /warehouse/bronze/inventory_customers/year=2024/month=01/day=15/data-001.parquet<br/>• /warehouse/bronze/inventory_customers/year=2024/month=01/day=15/data-002.parquet<br/>• /warehouse/bronze/inventory_customers/year=2024/month=01/day=15/manifest-list.avro<br/>• /warehouse/bronze/inventory_customers/year=2024/month=01/day=15/manifest.avro<br/><br/>🔐 Durability: Multiple replicas<br/>📊 Compression: ZSTD<br/>⚡ Query Performance: Columnar format"]
    end
    
    %% Nessie Catalog Entry
    subgraph "Nessie Catalog Management"
        NESSIE["📚 Nessie Catalog Entry<br/>Table: bronze.inventory_customers<br/>Branch: main<br/>Commit: abc123def456<br/>Metadata:<br/>• Schema version: 1<br/>• Partition spec: year/month/day<br/>• Snapshot ID: *********<br/>• Manifest list location<br/>• Statistics: row count, file count<br/>• Properties: format, compression<br/><br/>🔄 Version Control: Full history<br/>🌿 Branching: Supported<br/>⏰ Time Travel: Query any snapshot"]
    end
    
    %% Flow connections
    MYSQL_REC --> CDC_REC
    CDC_REC --> KAFKA_REC
    KAFKA_REC --> PARSED
    PARSED --> ENRICHED
    ENRICHED --> DTO
    DTO --> BATCH
    BATCH --> DICT_CONV
    DICT_CONV --> DF
    DF --> DF_PREP
    DF_PREP --> SCHEMA
    SCHEMA --> WRITE
    WRITE --> STORAGE
    WRITE --> NESSIE
    
    %% Styling
    classDef mysql fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef cdc fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef daft fill:#96ceb4,stroke:#333,stroke-width:2px,color:#000
    classDef iceberg fill:#feca57,stroke:#333,stroke-width:2px,color:#000
    classDef storage fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef catalog fill:#26de81,stroke:#333,stroke-width:2px,color:#000
    
    class MYSQL_REC mysql
    class CDC_REC cdc
    class KAFKA_REC kafka
    class PARSED,ENRICHED,DTO,BATCH,DICT_CONV processing
    class DF,DF_PREP daft
    class SCHEMA,WRITE iceberg
    class STORAGE storage
    class NESSIE catalog
```

## Sample DataFlow Description

### 1. Complete System Architecture Diagram
* Shows the full end-to-end flow from MySQL to MinIO storage
* Illustrates all components: FastAPI, Ray actors, Kafka consumers, transformers, writers, Iceberg infrastructure
* Includes error handling, monitoring, and infrastructure services

### 2. Detailed Method Call Sequence
* Step-by-step method calls showing exactly which classes and methods are invoked
* Shows the initialization sequence, main processing loop, and cleanup operations
* Demonstrates how your CDC data moves through each component with specific method names

### 3. Data Transformation Journey
* Shows how your actual MySQL customer record gets transformed at each stage
* Demonstrates the data structure evolution from MySQL → CDC → Kafka → Daft → Iceberg
* Shows the final storage format and organization in MinIO with Nessie catalog management

### 🔑 Key Insights from the Diagrams:
* Real-time Processing: Your MySQL changes are captured by Debezium and processed in near real-time through Ray actors
* Batching Efficiency: Records are batched (1000 records or 30 seconds) for optimal write performance
* Columnar Processing: Daft DataFrames provide efficient columnar operations on your CDC data
* ACID Compliance: Iceberg ensures data consistency with ACID transactions managed by Nessie
* Partitioned Storage: Data is automatically partitioned by year/month/day for query performance
* Schema Evolution: The system supports schema changes without breaking existing data
* Error Handling: Failed records are sent to Dead Letter Queues for investigation
* Monitoring: Multiple dashboards provide visibility into the entire pipeline

In this example your customer data flows seamlessly from MySQL changes through this modern data stack, landing in a queryable, versioned, and ACID-compliant data lake ready for analytics and further processing in the Silver and Gold layers.



## 🛠️ API Endpoints

The FastAPI application provides management endpoints:

- `POST /manager/start-consumers` - Start all consumer workers
- `POST /manager/stop-consumers` - Stop all consumer workers
- `GET /manager/fetch-consumers` - Get running consumer status
- `POST /manager/health` - Health check endpoint

## 📊 Monitoring and Observability

### Ray Dashboard
- Access at `http://localhost:8265`
- Monitor worker health, resource usage, and task execution

### Airflow UI
- Access at `http://localhost:8080` (admin/admin)
- Monitor DAG execution, task status, and logs

### MinIO Console
- Access at `http://localhost:9001` (minioadmin/minioadmin)
- Browse data lake contents and manage buckets

### Nessie UI
- Access at `http://localhost:19120`
- View catalog contents, branches, and commit history

## 🧪 Testing

### Run Unit Tests

```bash
# Run all tests
./scripts/run_tests.sh

# Run specific test file
pytest tests/test_daft_iceberg_transformer.py -v

# Run with coverage
pytest tests/ --cov=src --cov-report=html
```

### Integration Testing

```bash
# Start infrastructure
docker-compose up -d

# Wait for services to be ready
sleep 30

# Create test topics
docker exec kafka kafka-topics --create --topic user-events --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# Send test messages
echo '{"user_id": 123, "action": "login", "timestamp": "2024-01-15T10:30:00Z"}' | \
  docker exec -i kafka kafka-console-producer --topic user-events --bootstrap-server localhost:9092

# Start consumer and verify data flow
export WORKER_CONFIG_PATH=config/daft_iceberg_consumer_config.json
uvicorn src.event_consumer_app:app --port 8000
```

## 🚀 Production Deployment

### Environment Variables

```bash
# Ray Configuration
export RAY_HEAD_ADDRESS=ray://your-ray-head:10001
export LOCAL_MODE=N
export WORKER_NUM_CPUS=1.0

# Kafka Configuration
export KAFKA_BOOTSTRAP_SERVERS=your-kafka:9092
export KAFKA_SECURITY_PROTOCOL=SASL_SSL
export KAFKA_SASL_USERNAME=your-username
export KAFKA_SASL_PASSWORD=your-password

# Storage Configuration
export MINIO_ENDPOINT=your-s3-endpoint
export MINIO_ACCESS_KEY=your-access-key
export MINIO_SECRET_KEY=your-secret-key

# Catalog Configuration
export NESSIE_URI=https://your-nessie:19120/api/v1
export ICEBERG_WAREHOUSE_PATH=s3a://your-bucket/warehouse
```

### Scaling Considerations

1. **Ray Cluster**: Scale workers based on throughput requirements
2. **Kafka Partitions**: Increase partitions for higher parallelism
3. **Batch Sizes**: Tune batch sizes based on latency vs. throughput needs
4. **Resource Allocation**: Monitor CPU/memory usage and adjust accordingly

## 🔧 Troubleshooting

### Common Issues

1. **Ray Connection Issues**
   ```bash
   # Check Ray cluster status
   ray status

   # Restart Ray cluster
   ray stop
   ray start --head --port=6379
   ```

2. **Kafka Connection Issues**
   ```bash
   # Test Kafka connectivity
   docker exec kafka kafka-topics --list --bootstrap-server localhost:9092
   ```

3. **MinIO/Nessie Issues**
   ```bash
   # Check service health
   docker-compose ps
   docker-compose logs minio
   docker-compose logs nessie
   ```

4. **Airflow DAG Issues**
   ```bash
   # Check DAG status
   docker exec airflow-scheduler airflow dags list

   # View logs
   docker-compose logs airflow-scheduler
   ```

## 📚 Additional Resources

- [Ray Documentation](https://docs.ray.io/)
- [Daft Documentation](https://www.getdaft.io/projects/docs/)
- [Apache Iceberg Documentation](https://iceberg.apache.org/)
- [Project Nessie Documentation](https://projectnessie.org/)
- [Apache Airflow Documentation](https://airflow.apache.org/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.txt](LICENSE.txt) file for details.
```json
[
  {
    "consumer_name": "some_consumer_group_name",
    "topic_name": "first-topic",
    "number_of_workers": 2,
    "enable_auto_commit": false,
    "bootstrap_servers": "localhost:9092",
    "key_deserializer": "STRING_DES",
    "value_deserializer": "STRING_DES",
    "header_deserializer": null,
    "auto_offset_reset": "earliest",
    "max_poll_records": 20,
    "max_poll_interval_ms": 60000,
    "sink_configs": {
      "transformer_cls": "src.transformers.test_transformer.SampleTransformer",
      "num_retries": 3,
      "retry_delay_seconds": 1,
      "stream_writers": [
        "src.stream_writers.console_stream_writer.ConsoleStreamWriter"
      ]
    },
    "dlq_config": {
      "bootstrap_servers": "localhost:9092",
      "topic_name": "test-dlq",
      "key_serializer": "STRING_SER",
      "value_serializer": "STRING_SER",
      "acks": "all",
      "compression_type": "gzip",
      "retries": 3,
      "linger_ms": 10
    }
  }
]

```

Config info

Config Name|Description|default value|Is mandatory?|
-----------|-----------|------------|--------------|
consumer_name|This will be used as consumer group name| |Yes
number_of_workers|Number of consumers to create for a consumer group|1|No
sink_configs|Any config related to your sink task. Say, if your are writing to Elasticsearch then you may want to add ES endpoint in config| |Yes
dlq_config|Dead letter queue config| |No
For available Serializers/deserializers refer [ser_des_util.py](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/src/kafka_core/ser_des_util.py)

Rest of the configs are self explanatory. 

**<ins>Step 3 - Install the Requirements</ins>**

Install all dependencies in [requirement.txt](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/requirements.txt)
```shell
pip install -r <path/to/requirement.txt>
```

Install the code using `setup.py`.
This is needed for ray to find modules to pickle/unpickle.

Go to project root folder, where setup.py exists and run:
```shell
 pip install -e .
```

**<ins>Step 4 - Start ray head node</ins>**

If running in local, run below command:
```shell
 ray start --head --port=6379
```


**<ins>Step 5 - Set necessary Environment Variables</ins>**

Variable Name|Description|Is Mandatory?|Default Value|
-------------|------------|------------|-------------|
LOCAL_MODE| `Y` or `N`. Tells weather to run Kafka Consumer in single node or in a distributed setup.|N|Y|
RAY_HEAD_ADDRESS|Ex: `ray://************:10001`. Avoid creating this env variable, if head and driver/app running on same node|No|auto|
WORKER_CONFIG_PATH|worker [json conig](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/config/consumer_config.json) path|Yes||
APP_USERNAME|Username to setup Basic API Authentication|No|admin|
APP_PASSWORD|Password to setup Basic API Authentication|No|admin|
WORKER_NUM_CPUS|Number of CPUs to reserve per Consumer/Worker|No|0.25|
SECURITY_PROTOCOL|Pass the security protocol being used to connect to Kafka Brokers. Valid values are - PLAINTEXT, SASL_PLAINTEXT, SASL_SSL|No|None|
SASL_MECHANISM|Using SASL based Auth. Pass either of the valid values - PLAIN, SCRAM-SHA-256, SCRAM-SHA-512|No|None|
SASL_USERNAME|Pass SASL username if using SASL Auth to connect to Kafka|No|None|
SASL_PASSWORD|Pass SASL password if using SASL Auth to connect to Kafka|No|None

**<ins>Step 6 - Run the APP</ins>**
```shell
uvicorn src.event_consumer_app:app --port <port> --reload
```

**Run App in docker container**

<ins>Build Image</ins>
```shell
# run below in the project root folder
 build -t kafka-connect-ray .
```

<ins>Run Image</ins>
```shell
# add other environment variables as you need.
 docker run -e RAY_HEAD_ADDRESS=ray://localhost:10001 -e LOCAL_MODE=N  -dp 8002:8002 kafka-connect-ray
```

**IMPORTANT!!!!**

While creating ray cluster make sure to install code dependencies by running below command in 
your Node or VM or container:
```shell
pip install kafka-connect-dependency==0.1.1
```
This will let ray head and worker nodes find the modules. 

This setup is added in Ray K8 [cluster config yaml](https://github.com/bkatwal/distributed-kafka-ray-daft-iceberg-ELT/blob/main/k8/ray/ray-cluster-config.yaml#L74) file.

### License

The MIT License (MIT)

Copyright (c) Bardan Pokhrel - <EMAIL>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
associated documentation files (the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge, publish, distribute,
sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT
NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES
OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
