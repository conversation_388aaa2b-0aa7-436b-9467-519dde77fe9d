# Core framework dependencies
fastapi==0.116.1
uvicorn==0.35.0
cachetools==6.2.0
starlette==0.47.3
pydantic==2.11.7
ratelimit==2.2.1
ray[default,data,serve]==2.48.0
setuptools==80.9.0
kafka-python==2.2.15
fastavro==1.12.0
ipywidgets==8.1.7


# Data processing and analytics
daft[all]==0.5.21
pandas==2.3.2
pyarrow==21.0.0
numpy==2.3.2
polars==1.32.3

# Iceberg and data lake
pyiceberg[all]==0.9.1
boto3==1.40.18
s3fs==2025.7.0

# MinIO client
minio==7.2.16

# Nessie client (for Iceberg catalog)
pynessie==0.67.0

# Airflow for orchestration
apache-airflow==2.10.4
apache-airflow-providers-postgres==5.13.1
apache-airflow-providers-amazon==8.31.0

# Additional utilities
python-dotenv==1.1.1
sqlalchemy==2.0.43
psycopg2-binary==2.9.10
requests==2.32.5
urllib3==2.5.0

# Development and testing
pytest==8.4.1
pytest-asyncio==1.1.0
black==25.1.0
flake8==7.3.0
