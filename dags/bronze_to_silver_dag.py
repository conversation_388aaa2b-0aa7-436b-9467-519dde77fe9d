"""
Airflow DAG for Bronze to Silver layer processing.
Orchestrates data cleaning, validation, and standardization using Ray and Daft.
"""

import os
from datetime import datetime, timedelta
from typing import Dict, Any

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup

import ray
from src.elt.silver_processor import create_silver_layer_manager

# Default arguments for the DAG
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False,
}

# DAG configuration
DAG_ID = 'bronze_to_silver_processing'
SCHEDULE_INTERVAL = '0 2 * * *'  # Daily at 2 AM
TAGS = ['elt', 'bronze', 'silver', 'ray', 'daft', 'iceberg']

# Create the DAG
# dag = DAG(
#     DAG_ID,
#     default_args=default_args,
#     description='Process bronze layer data to silver layer with data quality and validation',
#     schedule_interval=SCHEDULE_INTERVAL,
#     tags=TAGS,
#     max_active_runs=1,
#     catchup=False,
# )


def initialize_ray_cluster(**context):
    """Initialize Ray cluster for distributed processing."""
    try:
        # Check if Ray is already initialized
        if not ray.is_initialized():
            ray_address = os.getenv('RAY_HEAD_ADDRESS', 'ray://localhost:10001')
            ray.init(address=ray_address)
        
        # Get cluster info
        cluster_info = ray.cluster_resources()
        context['task_instance'].xcom_push(key='cluster_info', value=cluster_info)
        
        print(f"Ray cluster initialized with resources: {cluster_info}")
        return "Ray cluster ready"
        
    except Exception as e:
        print(f"Failed to initialize Ray cluster: {str(e)}")
        raise


def get_bronze_tables_to_process(**context):
    """Get list of bronze tables that need processing."""
    try:
        from src.elt.iceberg_utils import get_iceberg_catalog_manager
        
        catalog_manager = get_iceberg_catalog_manager()
        bronze_tables = catalog_manager.list_tables('bronze')
        
        # Filter tables that need processing (could add logic here)
        tables_to_process = [table for table in bronze_tables if table]
        
        context['task_instance'].xcom_push(key='bronze_tables', value=tables_to_process)
        
        print(f"Found {len(tables_to_process)} bronze tables to process: {tables_to_process}")
        return tables_to_process
        
    except Exception as e:
        print(f"Failed to get bronze tables: {str(e)}")
        raise


def process_bronze_to_silver_table(table_name: str, **context):
    """Process a specific bronze table to silver layer."""
    try:
        # Get processing date
        processing_date = context['ds']  # Airflow execution date in YYYY-MM-DD format
        
        # Initialize silver layer manager
        config = {
            'bronze_namespace': 'bronze',
            'silver_namespace': 'silver',
            'num_silver_processors': 2,
        }
        
        silver_manager = create_silver_layer_manager(config)
        
        # Generate silver table name
        silver_table_name = f"{table_name}_clean"
        
        # Process bronze to silver
        result_refs = silver_manager.process_bronze_to_silver_batch(
            bronze_table_name=table_name,
            silver_table_name=silver_table_name,
            processing_dates=[processing_date]
        )
        
        # Wait for results
        results = ray.get(result_refs)
        
        # Log results
        for result in results:
            if result['status'] == 'success':
                print(f"Successfully processed {table_name}: {result}")
            else:
                print(f"Failed to process {table_name}: {result}")
                raise Exception(f"Processing failed: {result.get('error', 'Unknown error')}")
        
        # Cleanup
        silver_manager.shutdown()
        
        return f"Processed {table_name} to silver layer"
        
    except Exception as e:
        print(f"Failed to process table {table_name}: {str(e)}")
        raise


def validate_silver_data_quality(**context):
    """Validate data quality in silver layer."""
    try:
        from src.elt.iceberg_utils import get_iceberg_catalog_manager
        import daft
        
        catalog_manager = get_iceberg_catalog_manager()
        processing_date = context['ds']
        
        # Get list of silver tables
        silver_tables = catalog_manager.list_tables('silver')
        
        validation_results = {}
        
        for table_name in silver_tables:
            try:
                table = catalog_manager.get_table(table_name, 'silver')
                df = daft.read_iceberg(table)
                
                # Basic data quality checks
                total_records = len(df)
                null_records = len(df.where(df["value_parsed"].is_null()))
                
                validation_results[table_name] = {
                    'total_records': total_records,
                    'null_records': null_records,
                    'data_quality_score': (total_records - null_records) / total_records if total_records > 0 else 0,
                    'status': 'passed' if null_records / total_records < 0.1 else 'warning'
                }
                
            except Exception as e:
                validation_results[table_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        # Store validation results
        context['task_instance'].xcom_push(key='validation_results', value=validation_results)
        
        print(f"Data quality validation completed: {validation_results}")
        return validation_results
        
    except Exception as e:
        print(f"Data quality validation failed: {str(e)}")
        raise


def cleanup_ray_cluster(**context):
    """Cleanup Ray cluster resources."""
    try:
        if ray.is_initialized():
            ray.shutdown()
        print("Ray cluster cleaned up successfully")
        return "Cleanup completed"

    except Exception as e:
        print(f"Failed to cleanup Ray cluster: {str(e)}")
        # Don't raise exception for cleanup failures
        return f"Cleanup failed: {str(e)}"


# Define tasks
with DAG(
    DAG_ID,
    default_args=default_args,
    description='Process bronze layer data to silver layer with data quality and validation',
    schedule_interval=SCHEDULE_INTERVAL,
    tags=TAGS,
    max_active_runs=1,
    catchup=False,
) as dag:
    
    # Initialize Ray cluster
    init_ray = PythonOperator(
        task_id='initialize_ray_cluster',
        python_callable=initialize_ray_cluster,
        doc_md="""
        Initialize Ray cluster for distributed processing.
        Connects to the Ray head node and verifies cluster resources.
        """
    )
    
    # Get bronze tables to process
    get_tables = PythonOperator(
        task_id='get_bronze_tables',
        python_callable=get_bronze_tables_to_process,
        doc_md="""
        Discover bronze tables that need to be processed to silver layer.
        Returns list of table names for downstream processing.
        """
    )
    
    # Create task group for parallel table processing
    with TaskGroup('process_tables_to_silver') as process_tables_group:
        
        # This would be dynamically generated based on available tables
        # For now, we'll create tasks for common table types
        common_tables = ['user_events', 'transaction_logs', 'system_metrics']
        
        table_tasks = []
        for table in common_tables:
            task = PythonOperator(
                task_id=f'process_{table}',
                python_callable=process_bronze_to_silver_table,
                op_kwargs={'table_name': table},
                doc_md=f"""
                Process {table} from bronze to silver layer.
                Applies data cleaning, validation, and standardization.
                """
            )
            table_tasks.append(task)
    
    # Data quality validation
    validate_quality = PythonOperator(
        task_id='validate_silver_data_quality',
        python_callable=validate_silver_data_quality,
        doc_md="""
        Validate data quality in silver layer tables.
        Checks for null values, data completeness, and basic quality metrics.
        """
    )
    
    # Send notification (placeholder)
    send_notification = BashOperator(
        task_id='send_completion_notification',
        bash_command="""
        echo "Bronze to Silver processing completed for {{ ds }}"
        echo "Validation results: {{ ti.xcom_pull(task_ids='validate_silver_data_quality', key='validation_results') }}"
        """,
        doc_md="""
        Send notification about processing completion.
        In production, this would send email/Slack notifications.
        """
    )
    
    # Cleanup Ray cluster
    cleanup_ray = PythonOperator(
        task_id='cleanup_ray_cluster',
        python_callable=cleanup_ray_cluster,
        trigger_rule='all_done',  # Run even if upstream tasks fail
        doc_md="""
        Cleanup Ray cluster resources.
        Ensures proper resource cleanup regardless of task success/failure.
        """
    )
    
    # Define task dependencies
    init_ray >> get_tables >> process_tables_group >> validate_quality >> send_notification >> cleanup_ray


# Add DAG documentation
dag.doc_md = """
# Bronze to Silver Processing DAG

This DAG orchestrates the transformation of raw data from the bronze layer to the cleaned and validated silver layer.

## Process Overview

1. **Initialize Ray Cluster**: Connect to distributed Ray cluster for parallel processing
2. **Discover Tables**: Find bronze tables that need processing
3. **Parallel Processing**: Process multiple tables simultaneously using Ray actors
4. **Data Quality Validation**: Validate the quality of processed silver data
5. **Notification**: Send completion notifications
6. **Cleanup**: Clean up Ray cluster resources

## Data Transformations

The bronze to silver transformation includes:
- JSON parsing and validation
- Data type standardization
- Duplicate removal
- Data quality filtering
- Schema validation
- Metadata enrichment

## Configuration

- **Schedule**: Daily at 2 AM UTC
- **Retries**: 2 attempts with 5-minute delays
- **Parallelism**: Configurable number of Ray actors
- **Data Quality Thresholds**: Configurable quality metrics

## Monitoring

Monitor this DAG for:
- Processing duration and throughput
- Data quality scores
- Error rates and failure patterns
- Resource utilization in Ray cluster
"""
