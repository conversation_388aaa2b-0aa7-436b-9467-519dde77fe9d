"""
Airflow DAG for Silver to Gold layer processing.
Orchestrates business logic, aggregations, and analytics-ready transformations using Ray and <PERSON>ft.
"""

import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup

import ray
from src.elt.gold_processor import create_gold_layer_manager

# Default arguments for the DAG
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False,
}

# DAG configuration
DAG_ID = 'silver_to_gold_processing'
SCHEDULE_INTERVAL = '0 4 * * *'  # Daily at 4 AM (after bronze-to-silver)
TAGS = ['elt', 'silver', 'gold', 'ray', 'daft', 'iceberg', 'analytics']

# Create the DAG
# dag = DAG(
#     DAG_ID,
#     default_args=default_args,
#     description='Process silver layer data to gold layer with business logic and aggregations',
#     schedule_interval=SCHEDULE_INTERVAL,
#     tags=TAGS,
#     max_active_runs=1,
#     catchup=False,
# )


def initialize_ray_cluster(**context):
    """Initialize Ray cluster for distributed processing."""
    try:
        # Check if Ray is already initialized
        if not ray.is_initialized():
            ray_address = os.getenv('RAY_HEAD_ADDRESS', 'ray://localhost:10001')
            ray.init(address=ray_address)
        
        # Get cluster info
        cluster_info = ray.cluster_resources()
        context['task_instance'].xcom_push(key='cluster_info', value=cluster_info)
        
        print(f"Ray cluster initialized with resources: {cluster_info}")
        return "Ray cluster ready"
        
    except Exception as e:
        print(f"Failed to initialize Ray cluster: {str(e)}")
        raise


def check_silver_data_availability(**context):
    """Check if silver layer data is available for processing."""
    try:
        from src.elt.iceberg_utils import get_iceberg_catalog_manager
        import daft
        
        catalog_manager = get_iceberg_catalog_manager()
        processing_date = context['ds']
        
        # Parse processing date
        date_parts = processing_date.split('-')
        year, month = int(date_parts[0]), int(date_parts[1])
        
        # Check silver tables
        silver_tables = catalog_manager.list_tables('silver')
        available_tables = {}
        
        for table_name in silver_tables:
            try:
                table = catalog_manager.get_table(table_name, 'silver')
                df = daft.read_iceberg(table)
                
                # Check if data exists for the processing date
                filtered_df = df.where(
                    (df["year"] == year) & 
                    (df["month"] == month)
                )
                
                record_count = len(filtered_df)
                available_tables[table_name] = {
                    'available': record_count > 0,
                    'record_count': record_count
                }
                
            except Exception as e:
                available_tables[table_name] = {
                    'available': False,
                    'error': str(e)
                }
        
        context['task_instance'].xcom_push(key='available_tables', value=available_tables)
        
        print(f"Silver data availability check completed: {available_tables}")
        return available_tables
        
    except Exception as e:
        print(f"Failed to check silver data availability: {str(e)}")
        raise


def create_daily_user_analytics(**context):
    """Create daily user behavior analytics."""
    try:
        processing_date = context['ds']
        
        # Initialize gold layer manager
        config = {
            'silver_namespace': 'silver',
            'gold_namespace': 'gold',
            'num_gold_processors': 2,
        }
        
        gold_manager = create_gold_layer_manager(config)
        
        # Process daily analytics
        result_refs = gold_manager.process_daily_analytics(processing_date)
        
        # Wait for user summary result
        user_summary_result = ray.get(result_refs[0])  # First result is user summary
        
        if user_summary_result['status'] == 'success':
            print(f"Successfully created user analytics: {user_summary_result}")
        else:
            print(f"Failed to create user analytics: {user_summary_result}")
            raise Exception(f"User analytics failed: {user_summary_result.get('error', 'Unknown error')}")
        
        # Cleanup
        gold_manager.shutdown()
        
        return user_summary_result
        
    except Exception as e:
        print(f"Failed to create user analytics: {str(e)}")
        raise


def create_transaction_metrics(**context):
    """Create transaction metrics and financial KPIs."""
    try:
        processing_date = context['ds']
        
        # Initialize gold layer manager
        config = {
            'silver_namespace': 'silver',
            'gold_namespace': 'gold',
            'num_gold_processors': 2,
        }
        
        gold_manager = create_gold_layer_manager(config)
        
        # Process daily analytics
        result_refs = gold_manager.process_daily_analytics(processing_date)
        
        # Wait for transaction metrics result
        transaction_result = ray.get(result_refs[1])  # Second result is transaction metrics
        
        if transaction_result['status'] == 'success':
            print(f"Successfully created transaction metrics: {transaction_result}")
        else:
            print(f"Failed to create transaction metrics: {transaction_result}")
            raise Exception(f"Transaction metrics failed: {transaction_result.get('error', 'Unknown error')}")
        
        # Cleanup
        gold_manager.shutdown()
        
        return transaction_result
        
    except Exception as e:
        print(f"Failed to create transaction metrics: {str(e)}")
        raise


def create_system_health_dashboard(**context):
    """Create system health dashboard data."""
    try:
        processing_date = context['ds']
        
        # Initialize gold layer manager
        config = {
            'silver_namespace': 'silver',
            'gold_namespace': 'gold',
            'num_gold_processors': 2,
        }
        
        gold_manager = create_gold_layer_manager(config)
        
        # Process daily analytics
        result_refs = gold_manager.process_daily_analytics(processing_date)
        
        # Wait for system health result
        health_result = ray.get(result_refs[2])  # Third result is system health
        
        if health_result['status'] == 'success':
            print(f"Successfully created system health dashboard: {health_result}")
        else:
            print(f"Failed to create system health dashboard: {health_result}")
            raise Exception(f"System health dashboard failed: {health_result.get('error', 'Unknown error')}")
        
        # Cleanup
        gold_manager.shutdown()
        
        return health_result
        
    except Exception as e:
        print(f"Failed to create system health dashboard: {str(e)}")
        raise


def validate_gold_data_quality(**context):
    """Validate data quality in gold layer."""
    try:
        from src.elt.iceberg_utils import get_iceberg_catalog_manager
        import daft
        
        catalog_manager = get_iceberg_catalog_manager()
        processing_date = context['ds']
        
        # Get list of gold tables
        gold_tables = catalog_manager.list_tables('gold')
        
        validation_results = {}
        
        for table_name in gold_tables:
            try:
                table = catalog_manager.get_table(table_name, 'gold')
                df = daft.read_iceberg(table)
                
                # Basic data quality checks for gold layer
                total_records = len(df)
                
                # Check for today's data
                today_data = df.where(df["date"] == processing_date)
                today_records = len(today_data)
                
                validation_results[table_name] = {
                    'total_records': total_records,
                    'today_records': today_records,
                    'has_today_data': today_records > 0,
                    'status': 'passed' if today_records > 0 else 'warning'
                }
                
            except Exception as e:
                validation_results[table_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        # Store validation results
        context['task_instance'].xcom_push(key='gold_validation_results', value=validation_results)
        
        print(f"Gold data quality validation completed: {validation_results}")
        return validation_results
        
    except Exception as e:
        print(f"Gold data quality validation failed: {str(e)}")
        raise


def cleanup_ray_cluster(**context):
    """Cleanup Ray cluster resources."""
    try:
        if ray.is_initialized():
            ray.shutdown()
        print("Ray cluster cleaned up successfully")
        return "Cleanup completed"
        
    except Exception as e:
        print(f"Failed to cleanup Ray cluster: {str(e)}")
        # Don't raise exception for cleanup failures
        return f"Cleanup failed: {str(e)}"


# Define tasks
with DAG(
    DAG_ID,
    default_args=default_args,
    description='Process silver layer data to gold layer with business logic and aggregations',
    schedule_interval=SCHEDULE_INTERVAL,
    tags=TAGS,
    max_active_runs=1,
    catchup=False,
) as dag:
    
    # Initialize Ray cluster
    init_ray = PythonOperator(
        task_id='initialize_ray_cluster',
        python_callable=initialize_ray_cluster,
        doc_md="""
        Initialize Ray cluster for distributed processing.
        Connects to the Ray head node and verifies cluster resources.
        """
    )
    
    # Check silver data availability
    check_silver_data = PythonOperator(
        task_id='check_silver_data_availability',
        python_callable=check_silver_data_availability,
        doc_md="""
        Check if silver layer data is available for processing.
        Verifies that upstream bronze-to-silver processing completed successfully.
        """
    )
    
    # Create task group for parallel gold processing
    with TaskGroup('create_gold_analytics') as gold_analytics_group:
        
        # User analytics
        user_analytics = PythonOperator(
            task_id='create_user_analytics',
            python_callable=create_daily_user_analytics,
            doc_md="""
            Create daily user behavior analytics and KPIs.
            Processes user events to generate engagement metrics.
            """
        )
        
        # Transaction metrics
        transaction_metrics = PythonOperator(
            task_id='create_transaction_metrics',
            python_callable=create_transaction_metrics,
            doc_md="""
            Create transaction metrics and financial KPIs.
            Processes transaction data to generate revenue and performance metrics.
            """
        )
        
        # System health dashboard
        system_health = PythonOperator(
            task_id='create_system_health_dashboard',
            python_callable=create_system_health_dashboard,
            doc_md="""
            Create system health dashboard data.
            Processes system metrics to generate operational insights.
            """
        )
    
    # Data quality validation
    validate_quality = PythonOperator(
        task_id='validate_gold_data_quality',
        python_callable=validate_gold_data_quality,
        doc_md="""
        Validate data quality in gold layer tables.
        Ensures analytics data is complete and accurate.
        """
    )
    
    # Send notification
    send_notification = BashOperator(
        task_id='send_completion_notification',
        bash_command="""
        echo "Silver to Gold processing completed for {{ ds }}"
        echo "Gold validation results: {{ ti.xcom_pull(task_ids='validate_gold_data_quality', key='gold_validation_results') }}"
        """,
        doc_md="""
        Send notification about gold layer processing completion.
        In production, this would send email/Slack notifications with analytics summary.
        """
    )
    
    # Cleanup Ray cluster
    cleanup_ray = PythonOperator(
        task_id='cleanup_ray_cluster',
        python_callable=cleanup_ray_cluster,
        trigger_rule='all_done',  # Run even if upstream tasks fail
        doc_md="""
        Cleanup Ray cluster resources.
        Ensures proper resource cleanup regardless of task success/failure.
        """
    )
    
    # Define task dependencies
    init_ray >> check_silver_data >> gold_analytics_group >> validate_quality >> send_notification >> cleanup_ray


# Add DAG documentation
dag.doc_md = """
# Silver to Gold Processing DAG

This DAG orchestrates the transformation of cleaned data from the silver layer to business-ready analytics in the gold layer.

## Process Overview

1. **Initialize Ray Cluster**: Connect to distributed Ray cluster for parallel processing
2. **Check Silver Data**: Verify silver layer data availability for processing
3. **Parallel Analytics Creation**: Create multiple analytics datasets simultaneously
4. **Data Quality Validation**: Validate the quality of gold layer analytics
5. **Notification**: Send completion notifications with analytics summary
6. **Cleanup**: Clean up Ray cluster resources

## Analytics Created

The silver to gold transformation creates:
- **User Analytics**: Daily user behavior, engagement, and conversion metrics
- **Transaction Metrics**: Financial KPIs, revenue analytics, and transaction success rates
- **System Health**: Operational metrics, performance indicators, and alert thresholds

## Configuration

- **Schedule**: Daily at 4 AM UTC (after bronze-to-silver processing)
- **Retries**: 2 attempts with 5-minute delays
- **Parallelism**: Configurable number of Ray actors for analytics processing
- **Dependencies**: Waits for silver layer data availability

## Monitoring

Monitor this DAG for:
- Analytics processing duration and accuracy
- Business metric trends and anomalies
- Data completeness and quality scores
- Resource utilization in Ray cluster
"""
