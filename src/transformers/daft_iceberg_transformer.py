"""
Daft-Iceberg transformer for processing Kafka messages with Daft DataFrames
and preparing them for Iceberg storage with metadata enrichment.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional

import daft
from kafka.consumer.fetcher import ConsumerRecord

from src.transformers.transformer import StreamTransformer
from src.model.worker_dto import SinkRecordDTO, SinkOperation, SinkOperationType

logger = logging.getLogger(__name__)


class DaftIcebergTransformer(StreamTransformer):
    """
    Transformer that processes Kafka messages using Daft DataFrames and enriches
    them with metadata for Iceberg storage in the bronze layer.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Daft-Iceberg transformer.
        
        Args:
            config: Configuration dictionary containing transformer settings
        """
        super().__init__(config)
        self.add_kafka_metadata = config.get('add_kafka_metadata', True)
        self.add_processing_time = config.get('add_processing_time', True)
        self.parse_json_value = config.get('parse_json_value', True)
        self.flatten_json = config.get('flatten_json', False)
        
        logger.info("Initialized DaftIcebergTransformer with config: %s", config)
    
    def transform(self, consumer_record: ConsumerRecord) -> SinkRecordDTO:
        """
        Transform a Kafka consumer record into a SinkRecordDTO with enriched metadata.
        
        This method:
        1. Parses the JSON message from Kafka
        2. Enriches with Kafka metadata (offset, partition, timestamp)
        3. Adds processing timestamp
        4. Adds partitioning columns (year, month, day)
        5. Creates a SinkRecordDTO for downstream processing
        
        Args:
            consumer_record: Kafka consumer record
            
        Returns:
            SinkRecordDTO with enriched message data
            
        Raises:
            ValueError: If message parsing fails
            Exception: For other transformation errors
        """
        try:
            # Parse the message value
            message_data = self._parse_message_value(consumer_record.value)
            
            # Enrich with metadata
            enriched_message = self._enrich_with_metadata(
                message_data, consumer_record
            )
            
            # Create sink operation
            sink_operation = SinkOperation(
                sink_operation_type=SinkOperationType.UPSERT
            )
            
            # Create and return SinkRecordDTO
            return SinkRecordDTO(
                key=consumer_record.key,
                message=enriched_message,
                topic=consumer_record.topic,
                partition=consumer_record.partition,
                offset=consumer_record.offset,
                sink_operation=sink_operation
            )
            
        except Exception as e:
            logger.error(
                "Failed to transform message from topic %s, partition %s, offset %s: %s",
                consumer_record.topic, consumer_record.partition, 
                consumer_record.offset, str(e)
            )
            raise
    
    def _parse_message_value(self, value: str) -> Dict[str, Any]:
        """
        Parse the message value from Kafka.
        
        Args:
            value: Raw message value from Kafka
            
        Returns:
            Parsed message as dictionary
            
        Raises:
            ValueError: If JSON parsing fails
        """
        if not value:
            return {}
        
        try:
            if self.parse_json_value:
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    return parsed
                else:
                    # If not a dict, wrap it
                    return {"value": parsed}
            else:
                # Return as raw string
                return {"value": value}
                
        except json.JSONDecodeError as e:
            logger.warning("Failed to parse JSON value, using raw string: %s", str(e))
            return {"value": value}
    
    def _enrich_with_metadata(self, message_data: Dict[str, Any], 
                            consumer_record: ConsumerRecord) -> Dict[str, Any]:
        """
        Enrich message data with Kafka and processing metadata.
        
        Args:
            message_data: Parsed message data
            consumer_record: Kafka consumer record
            
        Returns:
            Enriched message with metadata
        """
        enriched = message_data.copy()
        
        # Add Kafka metadata if enabled
        if self.add_kafka_metadata:
            enriched.update({
                "_kafka_topic": consumer_record.topic,
                "_kafka_partition": consumer_record.partition,
                "_kafka_offset": consumer_record.offset,
                "_kafka_key": consumer_record.key,
            })
            
            # Add Kafka timestamp if available
            if consumer_record.timestamp is not None:
                kafka_timestamp = datetime.fromtimestamp(
                    consumer_record.timestamp / 1000.0, tz=timezone.utc
                )
                enriched["_kafka_timestamp"] = kafka_timestamp.isoformat()
            else:
                enriched["_kafka_timestamp"] = None
        
        # Add processing timestamp if enabled
        if self.add_processing_time:
            processing_time = datetime.now(timezone.utc)
            enriched["_processing_time"] = processing_time.isoformat()
            
            # Add partitioning columns based on processing time
            enriched.update({
                "_year": processing_time.year,
                "_month": processing_time.month,
                "_day": processing_time.day,
            })
        
        return enriched
    
    def create_daft_dataframe(self, records: list) -> daft.DataFrame:
        """
        Create a Daft DataFrame from a list of transformed records.
        
        This method is used by the stream writer to create DataFrames
        from batches of transformed records.
        
        Args:
            records: List of transformed message dictionaries
            
        Returns:
            Daft DataFrame
        """
        try:
            if not records:
                # Return empty DataFrame with expected schema
                return daft.from_pylist([])
            
            # Create DataFrame from records
            df = daft.from_pylist(records)
            
            # Add any additional transformations here
            df = self._apply_daft_transformations(df)
            
            return df
            
        except Exception as e:
            logger.error("Failed to create Daft DataFrame: %s", str(e))
            raise
    
    def _apply_daft_transformations(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Apply Daft-specific transformations to the DataFrame.
        
        Args:
            df: Input Daft DataFrame
            
        Returns:
            Transformed Daft DataFrame
        """
        try:
            # Ensure timestamp columns are properly typed
            if "_processing_time" in df.column_names:
                df = df.with_column(
                    "_processing_time", 
                    df["_processing_time"].str.strptime("%Y-%m-%dT%H:%M:%S.%f%z")
                )
            
            if "_kafka_timestamp" in df.column_names:
                # Handle null values in kafka timestamp
                df = df.with_column(
                    "_kafka_timestamp",
                    df["_kafka_timestamp"].if_else(
                        df["_kafka_timestamp"].is_null(),
                        None,
                        df["_kafka_timestamp"].str.strptime("%Y-%m-%dT%H:%M:%S.%f%z")
                    )
                )
            
            # Ensure partitioning columns are integers
            for col in ["_year", "_month", "_day"]:
                if col in df.column_names:
                    df = df.with_column(col, df[col].cast(daft.DataType.int64()))
            
            return df
            
        except Exception as e:
            logger.warning("Failed to apply Daft transformations, returning original DataFrame: %s", str(e))
            return df
    
    def get_bronze_table_name(self, topic: str) -> str:
        """
        Generate bronze table name from Kafka topic.
        
        Args:
            topic: Kafka topic name
            
        Returns:
            Bronze table name
        """
        # Replace hyphens and dots with underscores for valid table names
        table_name = topic.replace("-", "_").replace(".", "_")
        return f"bronze_{table_name}"
    
    def validate_message(self, message_data: Dict[str, Any]) -> bool:
        """
        Validate the transformed message data.
        
        Args:
            message_data: Transformed message data
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Basic validation - ensure we have required fields
            required_fields = ["_processing_time", "_year", "_month", "_day"]
            
            for field in required_fields:
                if field not in message_data:
                    logger.warning("Missing required field: %s", field)
                    return False
            
            # Validate year, month, day ranges
            year = message_data.get("_year")
            month = message_data.get("_month")
            day = message_data.get("_day")
            
            if not (2020 <= year <= 2030):  # Reasonable year range
                logger.warning("Invalid year: %s", year)
                return False
            
            if not (1 <= month <= 12):
                logger.warning("Invalid month: %s", month)
                return False
            
            if not (1 <= day <= 31):
                logger.warning("Invalid day: %s", day)
                return False
            
            return True
            
        except Exception as e:
            logger.warning("Message validation failed: %s", str(e))
            return False
