"""
Batched Daft-Iceberg stream writer that accumulates records, creates Daft DataFrames,
and writes to Iceberg tables with partitioning support.
"""

import logging
import time
import threading
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

import daft
from pyiceberg.table import Table

from src.stream_writers.stream_writer import StreamWriter
from src.model.worker_dto import SinkRecordDTO
from src.elt.iceberg_utils import IcebergCatalogManager, get_iceberg_catalog_manager

logger = logging.getLogger(__name__)


class BatchedDaftIcebergWriter(StreamWriter):
    """
    Stream writer that batches records and writes them to Iceberg tables using Daft DataFrames.
    
    Features:
    - Batching by size and time
    - Automatic table creation
    - Partitioned writes
    - Thread-safe operations
    - Error handling and retry logic
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the batched Daft-Iceberg writer.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Batching configuration
        self.batch_size = config.get('batch_size', 1000)
        self.batch_timeout_seconds = config.get('batch_timeout_seconds', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay_seconds = config.get('retry_delay_seconds', 1)
        
        # Table configuration
        self.namespace = config.get('namespace', 'bronze')
        self.auto_create_tables = config.get('auto_create_tables', True)
        
        # Initialize components
        self.catalog_manager: IcebergCatalogManager = get_iceberg_catalog_manager()
        
        # Batching state
        self.current_batch: List[SinkRecordDTO] = []
        self.last_write_time = time.time()
        self.batch_lock = threading.Lock()
        
        # Table cache
        self.table_cache: Dict[str, Table] = {}
        
        logger.info(
            "Initialized BatchedDaftIcebergWriter with batch_size=%d, timeout=%ds, namespace=%s",
            self.batch_size, self.batch_timeout_seconds, self.namespace
        )
    
    def write(self, streams: List[SinkRecordDTO]) -> None:
        """
        Write records to the batch. Flushes if batch is ready.
        
        Args:
            streams: List of SinkRecordDTO records to write
        """
        if not streams:
            return
        
        with self.batch_lock:
            # Add records to current batch
            self.current_batch.extend(streams)
            
            # Check if we should flush the batch
            should_flush = self._should_flush_batch()
            
            if should_flush:
                self._flush_batch()
    
    def _should_flush_batch(self) -> bool:
        """
        Determine if the current batch should be flushed.
        
        Returns:
            True if batch should be flushed, False otherwise
        """
        # Check batch size
        if len(self.current_batch) >= self.batch_size:
            logger.debug("Flushing batch due to size: %d >= %d", 
                        len(self.current_batch), self.batch_size)
            return True
        
        # Check timeout
        current_time = time.time()
        if (current_time - self.last_write_time) >= self.batch_timeout_seconds:
            if self.current_batch:  # Only flush if we have records
                logger.debug("Flushing batch due to timeout: %.2fs >= %ds",
                           current_time - self.last_write_time, self.batch_timeout_seconds)
                return True
        
        return False
    
    def _flush_batch(self) -> None:
        """
        Flush the current batch to Iceberg tables.
        This method assumes it's called within a lock.
        """
        if not self.current_batch:
            return
        
        batch_to_process = self.current_batch.copy()
        self.current_batch.clear()
        self.last_write_time = time.time()
        
        try:
            # Group records by topic for separate table writes
            records_by_topic = self._group_records_by_topic(batch_to_process)
            
            # Process each topic separately
            for topic, records in records_by_topic.items():
                self._write_topic_batch(topic, records)
            
            logger.info("Successfully flushed batch of %d records across %d topics",
                       len(batch_to_process), len(records_by_topic))
            
        except Exception as e:
            logger.error("Failed to flush batch: %s", str(e))
            # Re-add records to batch for retry (simple strategy)
            with self.batch_lock:
                self.current_batch.extend(batch_to_process)
            raise
    
    def _group_records_by_topic(self, records: List[SinkRecordDTO]) -> Dict[str, List[SinkRecordDTO]]:
        """
        Group records by Kafka topic.
        
        Args:
            records: List of SinkRecordDTO records
            
        Returns:
            Dictionary mapping topic to list of records
        """
        grouped = {}
        for record in records:
            topic = record.topic
            if topic not in grouped:
                grouped[topic] = []
            grouped[topic].append(record)
        
        return grouped
    
    def _write_topic_batch(self, topic: str, records: List[SinkRecordDTO]) -> None:
        """
        Write a batch of records for a specific topic to Iceberg.
        
        Args:
            topic: Kafka topic name
            records: List of records for this topic
        """
        try:
            # Get or create table
            table = self._get_or_create_table(topic)
            
            # Convert records to list of dictionaries
            record_dicts = [self._sink_record_to_dict(record) for record in records]
            
            # Create Daft DataFrame
            df = daft.from_pylist(record_dicts)
            
            # Apply transformations
            df = self._prepare_dataframe_for_iceberg(df)
            
            # Write to Iceberg with retries
            self._write_dataframe_to_iceberg(df, table, topic)
            
            logger.debug("Successfully wrote %d records to topic %s", len(records), topic)
            
        except Exception as e:
            logger.error("Failed to write batch for topic %s: %s", topic, str(e))
            raise
    
    def _get_or_create_table(self, topic: str) -> Table:
        """
        Get or create an Iceberg table for the given topic.
        
        Args:
            topic: Kafka topic name
            
        Returns:
            Iceberg table
        """
        table_name = self._get_table_name(topic)
        
        # Check cache first
        cache_key = f"{self.namespace}.{table_name}"
        if cache_key in self.table_cache:
            return self.table_cache[cache_key]
        
        try:
            # Try to load existing table
            table = self.catalog_manager.get_table(table_name, self.namespace)
            self.table_cache[cache_key] = table
            return table
            
        except Exception:
            if self.auto_create_tables:
                # Create new bronze table
                logger.info("Creating new bronze table for topic: %s", topic)
                table = self.catalog_manager.create_bronze_table(table_name, self.namespace)
                self.table_cache[cache_key] = table
                return table
            else:
                raise
    
    def _get_table_name(self, topic: str) -> str:
        """
        Generate table name from Kafka topic.
        
        Args:
            topic: Kafka topic name
            
        Returns:
            Table name
        """
        # Replace hyphens and dots with underscores for valid table names
        return topic.replace("-", "_").replace(".", "_")
    
    def _sink_record_to_dict(self, record: SinkRecordDTO) -> Dict[str, Any]:
        """
        Convert SinkRecordDTO to dictionary for Daft DataFrame.
        
        Args:
            record: SinkRecordDTO record
            
        Returns:
            Dictionary representation
        """
        # Extract message data
        message_data = record.message.copy() if record.message else {}
        
        # Add Iceberg table columns
        result = {
            "key": record.key,
            "value": str(message_data),  # Store as JSON string for bronze layer
            "topic": record.topic,
            "partition": record.partition,
            "offset": record.offset,
            "timestamp": datetime.now(timezone.utc),
            "kafka_timestamp": None,  # Will be set from message if available
            "processing_time": datetime.now(timezone.utc),
        }
        
        # Extract partitioning columns from message
        if "_year" in message_data:
            result["year"] = message_data["_year"]
        else:
            result["year"] = datetime.now(timezone.utc).year
            
        if "_month" in message_data:
            result["month"] = message_data["_month"]
        else:
            result["month"] = datetime.now(timezone.utc).month
            
        if "_day" in message_data:
            result["day"] = message_data["_day"]
        else:
            result["day"] = datetime.now(timezone.utc).day
        
        # Extract Kafka timestamp if available
        if "_kafka_timestamp" in message_data:
            try:
                kafka_ts_str = message_data["_kafka_timestamp"]
                if kafka_ts_str:
                    result["kafka_timestamp"] = datetime.fromisoformat(
                        kafka_ts_str.replace('Z', '+00:00')
                    )
            except Exception as e:
                logger.warning("Failed to parse kafka timestamp: %s", str(e))
        
        return result
    
    def _prepare_dataframe_for_iceberg(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Prepare Daft DataFrame for Iceberg write.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Prepared DataFrame
        """
        try:
            # Ensure proper data types
            df = df.with_column("partition", df["partition"].cast(daft.DataType.int64()))
            df = df.with_column("offset", df["offset"].cast(daft.DataType.int64()))
            df = df.with_column("year", df["year"].cast(daft.DataType.int64()))
            df = df.with_column("month", df["month"].cast(daft.DataType.int64()))
            df = df.with_column("day", df["day"].cast(daft.DataType.int64()))
            
            return df
            
        except Exception as e:
            logger.warning("Failed to prepare DataFrame, using original: %s", str(e))
            return df
    
    def _write_dataframe_to_iceberg(self, df: daft.DataFrame, table: Table, topic: str) -> None:
        """
        Write Daft DataFrame to Iceberg table with retries.
        
        Args:
            df: Daft DataFrame to write
            table: Iceberg table
            topic: Kafka topic name (for logging)
        """
        for attempt in range(self.max_retries + 1):
            try:
                # Write to Iceberg
                df.write_iceberg(table, mode="append")
                return  # Success
                
            except Exception as e:
                if attempt < self.max_retries:
                    logger.warning(
                        "Write attempt %d failed for topic %s, retrying: %s",
                        attempt + 1, topic, str(e)
                    )
                    time.sleep(self.retry_delay_seconds * (attempt + 1))  # Exponential backoff
                else:
                    logger.error(
                        "All write attempts failed for topic %s: %s",
                        topic, str(e)
                    )
                    raise
    
    def force_flush(self) -> None:
        """
        Force flush any remaining records in the batch.
        """
        with self.batch_lock:
            if self.current_batch:
                logger.info("Force flushing %d remaining records", len(self.current_batch))
                self._flush_batch()
    
    def close(self) -> None:
        """
        Close the writer and flush any remaining records.
        """
        try:
            self.force_flush()
            logger.info("BatchedDaftIcebergWriter closed successfully")
        except Exception as e:
            logger.error("Error during writer close: %s", str(e))
