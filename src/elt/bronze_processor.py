"""
Bronze layer processor for raw data ingestion from Kafka to Iceberg.
Handles minimal transformation and stores raw data with metadata.
"""

import logging
import ray
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

import daft
from pyiceberg.table import Table

from src.elt.iceberg_utils import IcebergCatalogManager, get_iceberg_catalog_manager

logger = logging.getLogger(__name__)


@ray.remote
class BronzeProcessor:
    """
    Ray actor for processing bronze layer data.
    Handles raw data ingestion from Kafka with minimal transformation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize bronze processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.catalog_manager = get_iceberg_catalog_manager()
        self.namespace = self.config.get('bronze_namespace', 'bronze')
        
        logger.info("Initialized BronzeProcessor with namespace: %s", self.namespace)
    
    def process_kafka_batch(self, topic: str, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process a batch of Kafka records for bronze layer storage.
        
        Args:
            topic: Kafka topic name
            records: List of raw Kafka records
            
        Returns:
            Processing result summary
        """
        try:
            if not records:
                return {"status": "success", "records_processed": 0, "message": "No records to process"}
            
            # Get or create bronze table
            table_name = self._get_bronze_table_name(topic)
            table = self._get_or_create_bronze_table(table_name)
            
            # Transform records for bronze storage
            bronze_records = self._transform_to_bronze_format(records, topic)
            
            # Create Daft DataFrame
            df = daft.from_pylist(bronze_records)
            
            # Apply bronze transformations
            df = self._apply_bronze_transformations(df)
            
            # Write to Iceberg
            df.write_iceberg(table, mode="append")
            
            result = {
                "status": "success",
                "records_processed": len(records),
                "table_name": f"{self.namespace}.{table_name}",
                "processing_time": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info("Processed %d records for topic %s to bronze layer", len(records), topic)
            return result
            
        except Exception as e:
            error_msg = f"Failed to process bronze batch for topic {topic}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "records_processed": 0
            }
    
    def _get_bronze_table_name(self, topic: str) -> str:
        """Generate bronze table name from topic."""
        return topic.replace("-", "_").replace(".", "_")
    
    def _get_or_create_bronze_table(self, table_name: str) -> Table:
        """Get or create bronze table."""
        try:
            return self.catalog_manager.get_table(table_name, self.namespace)
        except Exception:
            logger.info("Creating new bronze table: %s.%s", self.namespace, table_name)
            return self.catalog_manager.create_bronze_table(table_name, self.namespace)
    
    def _transform_to_bronze_format(self, records: List[Dict[str, Any]], topic: str) -> List[Dict[str, Any]]:
        """
        Transform raw Kafka records to bronze format.
        
        Args:
            records: Raw Kafka records
            topic: Kafka topic name
            
        Returns:
            List of bronze-formatted records
        """
        bronze_records = []
        processing_time = datetime.now(timezone.utc)
        
        for record in records:
            bronze_record = {
                "key": record.get("key"),
                "value": str(record.get("value", "")),  # Store as string in bronze
                "topic": topic,
                "partition": record.get("partition", 0),
                "offset": record.get("offset", 0),
                "timestamp": processing_time,
                "kafka_timestamp": self._parse_kafka_timestamp(record.get("kafka_timestamp")),
                "processing_time": processing_time,
                "year": processing_time.year,
                "month": processing_time.month,
                "day": processing_time.day,
            }
            bronze_records.append(bronze_record)
        
        return bronze_records
    
    def _parse_kafka_timestamp(self, kafka_timestamp: Any) -> Optional[datetime]:
        """Parse Kafka timestamp to datetime."""
        if kafka_timestamp is None:
            return None
        
        try:
            if isinstance(kafka_timestamp, (int, float)):
                return datetime.fromtimestamp(kafka_timestamp / 1000.0, tz=timezone.utc)
            elif isinstance(kafka_timestamp, str):
                return datetime.fromisoformat(kafka_timestamp.replace('Z', '+00:00'))
            else:
                return None
        except Exception as e:
            logger.warning("Failed to parse Kafka timestamp %s: %s", kafka_timestamp, str(e))
            return None
    
    def _apply_bronze_transformations(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Apply bronze-specific transformations to DataFrame.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Transformed DataFrame
        """
        try:
            # Ensure proper data types for Iceberg
            df = df.with_column("partition", df["partition"].cast(daft.DataType.int64()))
            df = df.with_column("offset", df["offset"].cast(daft.DataType.int64()))
            df = df.with_column("year", df["year"].cast(daft.DataType.int64()))
            df = df.with_column("month", df["month"].cast(daft.DataType.int64()))
            df = df.with_column("day", df["day"].cast(daft.DataType.int64()))
            
            # Add data quality flags
            df = df.with_column("is_valid", df["value"].is_not_null())
            df = df.with_column("record_size", df["value"].str.length())
            
            return df
            
        except Exception as e:
            logger.warning("Failed to apply bronze transformations: %s", str(e))
            return df
    
    def get_bronze_table_stats(self, table_name: str) -> Dict[str, Any]:
        """
        Get statistics for a bronze table.
        
        Args:
            table_name: Bronze table name
            
        Returns:
            Table statistics
        """
        try:
            table = self.catalog_manager.get_table(table_name, self.namespace)
            
            # Read table and compute basic stats
            df = daft.read_iceberg(table)
            
            stats = {
                "table_name": f"{self.namespace}.{table_name}",
                "total_records": len(df),
                "schema": str(table.schema()),
                "partitions": str(table.spec()),
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
            
            # Add column-specific stats if data exists
            if len(df) > 0:
                stats.update({
                    "unique_topics": df.select("topic").distinct().count(),
                    "date_range": {
                        "min_date": df.select("timestamp").min().collect()[0]["timestamp"],
                        "max_date": df.select("timestamp").max().collect()[0]["timestamp"]
                    }
                })
            
            return stats
            
        except Exception as e:
            logger.error("Failed to get bronze table stats for %s: %s", table_name, str(e))
            return {"error": str(e)}


class BronzeLayerManager:
    """
    Manager for bronze layer operations.
    Coordinates multiple bronze processors and provides high-level operations.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize bronze layer manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.num_processors = self.config.get('num_bronze_processors', 2)
        self.processors = []
        
        # Initialize Ray actors
        self._initialize_processors()
        
        logger.info("Initialized BronzeLayerManager with %d processors", self.num_processors)
    
    def _initialize_processors(self):
        """Initialize bronze processor Ray actors."""
        for i in range(self.num_processors):
            processor = BronzeProcessor.remote(self.config)
            self.processors.append(processor)
    
    def process_topic_batch(self, topic: str, records: List[Dict[str, Any]]) -> ray.ObjectRef:
        """
        Process a batch of records for a topic using available processors.
        
        Args:
            topic: Kafka topic name
            records: List of records to process
            
        Returns:
            Ray ObjectRef for the processing result
        """
        # Simple round-robin assignment
        processor_idx = hash(topic) % len(self.processors)
        processor = self.processors[processor_idx]
        
        return processor.process_kafka_batch.remote(topic, records)
    
    def get_all_bronze_tables(self) -> List[str]:
        """
        Get list of all bronze tables.
        
        Returns:
            List of bronze table names
        """
        try:
            catalog_manager = get_iceberg_catalog_manager()
            namespace = self.config.get('bronze_namespace', 'bronze')
            return catalog_manager.list_tables(namespace)
        except Exception as e:
            logger.error("Failed to list bronze tables: %s", str(e))
            return []
    
    def get_table_stats(self, table_name: str) -> ray.ObjectRef:
        """
        Get statistics for a bronze table.
        
        Args:
            table_name: Bronze table name
            
        Returns:
            Ray ObjectRef for the table statistics
        """
        # Use first processor for stats
        processor = self.processors[0]
        return processor.get_bronze_table_stats.remote(table_name)
    
    def shutdown(self):
        """Shutdown all bronze processors."""
        for processor in self.processors:
            ray.kill(processor)
        self.processors.clear()
        logger.info("Shutdown BronzeLayerManager")


def create_bronze_layer_manager(config: Optional[Dict[str, Any]] = None) -> BronzeLayerManager:
    """
    Factory function to create bronze layer manager.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        BronzeLayerManager instance
    """
    return BronzeLayerManager(config)
