"""
Silver layer processor for data cleaning, validation, and standardization.
Transforms bronze data into clean, validated, and standardized format.
"""

import json
import logging
import ray
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone

import daft
from pyiceberg.table import Table
from pyiceberg.schema import Schema
from pyiceberg.types import (
    NestedField, StringType, LongType, TimestampType, 
    BooleanType, DoubleType, StructType
)

from src.elt.iceberg_utils import IcebergCatalogManager, get_iceberg_catalog_manager

logger = logging.getLogger(__name__)


@ray.remote
class SilverProcessor:
    """
    Ray actor for processing silver layer data.
    Handles data cleaning, validation, and standardization from bronze layer.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize silver processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.catalog_manager = get_iceberg_catalog_manager()
        self.bronze_namespace = self.config.get('bronze_namespace', 'bronze')
        self.silver_namespace = self.config.get('silver_namespace', 'silver')
        
        # Data quality thresholds
        self.min_record_size = self.config.get('min_record_size', 10)
        self.max_record_size = self.config.get('max_record_size', 1000000)
        self.duplicate_window_hours = self.config.get('duplicate_window_hours', 24)
        
        logger.info("Initialized SilverProcessor with namespaces: bronze=%s, silver=%s", 
                   self.bronze_namespace, self.silver_namespace)
    
    def process_bronze_to_silver(self, bronze_table_name: str, 
                                silver_table_name: str,
                                processing_date: str) -> Dict[str, Any]:
        """
        Process bronze data to silver layer for a specific date.
        
        Args:
            bronze_table_name: Source bronze table name
            silver_table_name: Target silver table name
            processing_date: Date to process (YYYY-MM-DD format)
            
        Returns:
            Processing result summary
        """
        try:
            # Load bronze data for the specified date
            bronze_df = self._load_bronze_data(bronze_table_name, processing_date)
            
            if bronze_df is None or len(bronze_df) == 0:
                return {
                    "status": "success",
                    "records_processed": 0,
                    "message": f"No bronze data found for {processing_date}"
                }
            
            # Apply silver transformations
            silver_df = self._transform_bronze_to_silver(bronze_df, bronze_table_name)
            
            # Get or create silver table
            silver_table = self._get_or_create_silver_table(silver_table_name, silver_df)
            
            # Write to silver table
            silver_df.write_iceberg(silver_table, mode="append")
            
            # Compute processing stats
            stats = self._compute_processing_stats(bronze_df, silver_df)
            
            result = {
                "status": "success",
                "bronze_table": f"{self.bronze_namespace}.{bronze_table_name}",
                "silver_table": f"{self.silver_namespace}.{silver_table_name}",
                "processing_date": processing_date,
                "processing_time": datetime.now(timezone.utc).isoformat(),
                **stats
            }
            
            logger.info("Processed %d bronze records to %d silver records for %s",
                       stats["bronze_records"], stats["silver_records"], processing_date)
            return result
            
        except Exception as e:
            error_msg = f"Failed to process bronze to silver for {processing_date}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "processing_date": processing_date
            }
    
    def _load_bronze_data(self, table_name: str, processing_date: str) -> Optional[daft.DataFrame]:
        """
        Load bronze data for a specific date.
        
        Args:
            table_name: Bronze table name
            processing_date: Date to process (YYYY-MM-DD)
            
        Returns:
            Daft DataFrame with bronze data
        """
        try:
            bronze_table = self.catalog_manager.get_table(table_name, self.bronze_namespace)
            
            # Parse processing date
            date_parts = processing_date.split('-')
            year, month, day = int(date_parts[0]), int(date_parts[1]), int(date_parts[2])
            
            # Read data with date filter
            df = daft.read_iceberg(bronze_table)
            df = df.where(
                (df["year"] == year) & 
                (df["month"] == month) & 
                (df["day"] == day)
            )
            
            return df
            
        except Exception as e:
            logger.error("Failed to load bronze data for %s on %s: %s", 
                        table_name, processing_date, str(e))
            return None
    
    def _transform_bronze_to_silver(self, bronze_df: daft.DataFrame, 
                                   table_name: str) -> daft.DataFrame:
        """
        Transform bronze DataFrame to silver format.
        
        Args:
            bronze_df: Bronze DataFrame
            table_name: Table name for context
            
        Returns:
            Silver DataFrame
        """
        try:
            # Start with bronze data
            df = bronze_df
            
            # Parse JSON values
            df = self._parse_json_values(df)
            
            # Apply data quality filters
            df = self._apply_data_quality_filters(df)
            
            # Deduplicate records
            df = self._deduplicate_records(df)
            
            # Standardize data types
            df = self._standardize_data_types(df)
            
            # Add silver metadata
            df = self._add_silver_metadata(df)
            
            # Apply table-specific transformations
            df = self._apply_table_specific_transformations(df, table_name)
            
            return df
            
        except Exception as e:
            logger.error("Failed to transform bronze to silver: %s", str(e))
            raise
    
    def _parse_json_values(self, df: daft.DataFrame) -> daft.DataFrame:
        """Parse JSON values from bronze layer."""
        try:
            # Try to parse the 'value' column as JSON
            # For now, we'll keep it as string and add parsed columns
            df = df.with_column("value_parsed", df["value"])
            df = df.with_column("is_valid_json", df["value"].str.length() > 0)
            
            return df
            
        except Exception as e:
            logger.warning("Failed to parse JSON values: %s", str(e))
            return df
    
    def _apply_data_quality_filters(self, df: daft.DataFrame) -> daft.DataFrame:
        """Apply data quality filters."""
        try:
            # Filter by record size
            df = df.where(
                (df["record_size"] >= self.min_record_size) &
                (df["record_size"] <= self.max_record_size)
            )
            
            # Filter out null or empty values
            df = df.where(df["value"].is_not_null())
            df = df.where(df["value"].str.length() > 0)
            
            # Filter out invalid records
            df = df.where(df["is_valid"] == True)
            
            return df
            
        except Exception as e:
            logger.warning("Failed to apply data quality filters: %s", str(e))
            return df
    
    def _deduplicate_records(self, df: daft.DataFrame) -> daft.DataFrame:
        """Remove duplicate records based on key and offset."""
        try:
            # Deduplicate based on topic, partition, offset
            df = df.distinct(subset=["topic", "partition", "offset"])
            
            return df
            
        except Exception as e:
            logger.warning("Failed to deduplicate records: %s", str(e))
            return df
    
    def _standardize_data_types(self, df: daft.DataFrame) -> daft.DataFrame:
        """Standardize data types for silver layer."""
        try:
            # Ensure consistent timestamp formats
            df = df.with_column("processed_at", df["processing_time"])
            df = df.with_column("ingested_at", df["timestamp"])
            
            # Standardize string fields
            df = df.with_column("topic_clean", df["topic"].str.lower())
            
            return df
            
        except Exception as e:
            logger.warning("Failed to standardize data types: %s", str(e))
            return df
    
    def _add_silver_metadata(self, df: daft.DataFrame) -> daft.DataFrame:
        """Add silver layer metadata."""
        try:
            current_time = datetime.now(timezone.utc)
            
            df = df.with_column("silver_processed_at", current_time)
            df = df.with_column("silver_processing_version", "1.0")
            df = df.with_column("data_quality_score", 1.0)  # Default high quality
            
            return df
            
        except Exception as e:
            logger.warning("Failed to add silver metadata: %s", str(e))
            return df
    
    def _apply_table_specific_transformations(self, df: daft.DataFrame, 
                                            table_name: str) -> daft.DataFrame:
        """Apply transformations specific to the table/topic."""
        try:
            # Example: user_events specific transformations
            if "user_events" in table_name:
                df = self._transform_user_events(df)
            elif "transaction" in table_name:
                df = self._transform_transactions(df)
            elif "system_metrics" in table_name:
                df = self._transform_system_metrics(df)
            
            return df
            
        except Exception as e:
            logger.warning("Failed to apply table-specific transformations: %s", str(e))
            return df
    
    def _transform_user_events(self, df: daft.DataFrame) -> daft.DataFrame:
        """Transform user events data."""
        # Add user event specific transformations
        return df
    
    def _transform_transactions(self, df: daft.DataFrame) -> daft.DataFrame:
        """Transform transaction data."""
        # Add transaction specific transformations
        return df
    
    def _transform_system_metrics(self, df: daft.DataFrame) -> daft.DataFrame:
        """Transform system metrics data."""
        # Add system metrics specific transformations
        return df
    
    def _get_or_create_silver_table(self, table_name: str, 
                                   sample_df: daft.DataFrame) -> Table:
        """Get or create silver table with appropriate schema."""
        try:
            return self.catalog_manager.get_table(table_name, self.silver_namespace)
        except Exception:
            # Create new silver table with schema derived from DataFrame
            schema = self._create_silver_schema(sample_df)
            logger.info("Creating new silver table: %s.%s", self.silver_namespace, table_name)
            return self.catalog_manager.create_silver_table(table_name, schema, self.silver_namespace)
    
    def _create_silver_schema(self, df: daft.DataFrame) -> Schema:
        """Create Iceberg schema for silver table."""
        # Define a standard silver schema
        return Schema(
            NestedField(1, "key", StringType(), required=False),
            NestedField(2, "value_parsed", StringType(), required=True),
            NestedField(3, "topic_clean", StringType(), required=True),
            NestedField(4, "partition", LongType(), required=True),
            NestedField(5, "offset", LongType(), required=True),
            NestedField(6, "ingested_at", TimestampType(), required=True),
            NestedField(7, "processed_at", TimestampType(), required=True),
            NestedField(8, "silver_processed_at", TimestampType(), required=True),
            NestedField(9, "silver_processing_version", StringType(), required=True),
            NestedField(10, "data_quality_score", DoubleType(), required=True),
            NestedField(11, "is_valid_json", BooleanType(), required=True),
            NestedField(12, "year", LongType(), required=True),
            NestedField(13, "month", LongType(), required=True),
        )
    
    def _compute_processing_stats(self, bronze_df: daft.DataFrame, 
                                silver_df: daft.DataFrame) -> Dict[str, Any]:
        """Compute processing statistics."""
        try:
            bronze_count = len(bronze_df)
            silver_count = len(silver_df)
            
            return {
                "bronze_records": bronze_count,
                "silver_records": silver_count,
                "records_filtered": bronze_count - silver_count,
                "filter_rate": (bronze_count - silver_count) / bronze_count if bronze_count > 0 else 0,
                "data_quality_score": silver_count / bronze_count if bronze_count > 0 else 1.0
            }
            
        except Exception as e:
            logger.warning("Failed to compute processing stats: %s", str(e))
            return {
                "bronze_records": 0,
                "silver_records": 0,
                "records_filtered": 0,
                "filter_rate": 0.0,
                "data_quality_score": 0.0
            }


class SilverLayerManager:
    """
    Manager for silver layer operations.
    Coordinates multiple silver processors and provides high-level operations.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize silver layer manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.num_processors = self.config.get('num_silver_processors', 2)
        self.processors = []
        
        # Initialize Ray actors
        self._initialize_processors()
        
        logger.info("Initialized SilverLayerManager with %d processors", self.num_processors)
    
    def _initialize_processors(self):
        """Initialize silver processor Ray actors."""
        for i in range(self.num_processors):
            processor = SilverProcessor.remote(self.config)
            self.processors.append(processor)
    
    def process_bronze_to_silver_batch(self, bronze_table_name: str,
                                     silver_table_name: str,
                                     processing_dates: List[str]) -> List[ray.ObjectRef]:
        """
        Process multiple dates from bronze to silver.
        
        Args:
            bronze_table_name: Source bronze table name
            silver_table_name: Target silver table name
            processing_dates: List of dates to process
            
        Returns:
            List of Ray ObjectRefs for processing results
        """
        results = []
        
        for i, date in enumerate(processing_dates):
            processor = self.processors[i % len(self.processors)]
            result = processor.process_bronze_to_silver.remote(
                bronze_table_name, silver_table_name, date
            )
            results.append(result)
        
        return results
    
    def shutdown(self):
        """Shutdown all silver processors."""
        for processor in self.processors:
            ray.kill(processor)
        self.processors.clear()
        logger.info("Shutdown SilverLayerManager")


def create_silver_layer_manager(config: Optional[Dict[str, Any]] = None) -> SilverLayerManager:
    """
    Factory function to create silver layer manager.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        SilverLayerManager instance
    """
    return SilverLayerManager(config)
