"""
Gold layer processor for business logic, aggregations, and analytics-ready data.
Transforms silver data into business-ready datasets with KPIs and metrics.
"""

import logging
import ray
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta

import daft
from pyiceberg.table import Table
from pyiceberg.schema import Schema
from pyiceberg.types import (
    NestedField, StringType, LongType, TimestampType, 
    BooleanType, DoubleType, StructType
)

from src.elt.iceberg_utils import IcebergCatalogManager, get_iceberg_catalog_manager

logger = logging.getLogger(__name__)


@ray.remote
class GoldProcessor:
    """
    Ray actor for processing gold layer data.
    Handles business logic, aggregations, and analytics-ready transformations.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize gold processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.catalog_manager = get_iceberg_catalog_manager()
        self.silver_namespace = self.config.get('silver_namespace', 'silver')
        self.gold_namespace = self.config.get('gold_namespace', 'gold')
        
        logger.info("Initialized GoldProcessor with namespaces: silver=%s, gold=%s", 
                   self.silver_namespace, self.gold_namespace)
    
    def create_daily_user_summary(self, processing_date: str) -> Dict[str, Any]:
        """
        Create daily user behavior summary from silver user events.
        
        Args:
            processing_date: Date to process (YYYY-MM-DD format)
            
        Returns:
            Processing result summary
        """
        try:
            # Load silver user events data
            silver_df = self._load_silver_data("user_events", processing_date)
            
            if silver_df is None or len(silver_df) == 0:
                return {
                    "status": "success",
                    "records_processed": 0,
                    "message": f"No silver user events data found for {processing_date}"
                }
            
            # Create user behavior aggregations
            user_summary_df = self._create_user_behavior_aggregations(silver_df, processing_date)
            
            # Get or create gold table
            gold_table = self._get_or_create_gold_table(
                "daily_user_summary", 
                self._create_user_summary_schema()
            )
            
            # Write to gold table
            user_summary_df.write_iceberg(gold_table, mode="append")
            
            result = {
                "status": "success",
                "gold_table": f"{self.gold_namespace}.daily_user_summary",
                "processing_date": processing_date,
                "records_created": len(user_summary_df),
                "processing_time": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info("Created daily user summary for %s: %d records", 
                       processing_date, len(user_summary_df))
            return result
            
        except Exception as e:
            error_msg = f"Failed to create daily user summary for {processing_date}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "processing_date": processing_date
            }
    
    def create_transaction_metrics(self, processing_date: str) -> Dict[str, Any]:
        """
        Create transaction metrics and financial KPIs from silver transaction data.
        
        Args:
            processing_date: Date to process (YYYY-MM-DD format)
            
        Returns:
            Processing result summary
        """
        try:
            # Load silver transaction data
            silver_df = self._load_silver_data("transaction_logs", processing_date)
            
            if silver_df is None or len(silver_df) == 0:
                return {
                    "status": "success",
                    "records_processed": 0,
                    "message": f"No silver transaction data found for {processing_date}"
                }
            
            # Create transaction metrics
            metrics_df = self._create_transaction_metrics(silver_df, processing_date)
            
            # Get or create gold table
            gold_table = self._get_or_create_gold_table(
                "daily_transaction_metrics", 
                self._create_transaction_metrics_schema()
            )
            
            # Write to gold table
            metrics_df.write_iceberg(gold_table, mode="append")
            
            result = {
                "status": "success",
                "gold_table": f"{self.gold_namespace}.daily_transaction_metrics",
                "processing_date": processing_date,
                "records_created": len(metrics_df),
                "processing_time": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info("Created transaction metrics for %s: %d records", 
                       processing_date, len(metrics_df))
            return result
            
        except Exception as e:
            error_msg = f"Failed to create transaction metrics for {processing_date}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "processing_date": processing_date
            }
    
    def create_system_health_dashboard(self, processing_date: str) -> Dict[str, Any]:
        """
        Create system health dashboard data from silver system metrics.
        
        Args:
            processing_date: Date to process (YYYY-MM-DD format)
            
        Returns:
            Processing result summary
        """
        try:
            # Load silver system metrics data
            silver_df = self._load_silver_data("system_metrics", processing_date)
            
            if silver_df is None or len(silver_df) == 0:
                return {
                    "status": "success",
                    "records_processed": 0,
                    "message": f"No silver system metrics data found for {processing_date}"
                }
            
            # Create system health metrics
            health_df = self._create_system_health_metrics(silver_df, processing_date)
            
            # Get or create gold table
            gold_table = self._get_or_create_gold_table(
                "daily_system_health", 
                self._create_system_health_schema()
            )
            
            # Write to gold table
            health_df.write_iceberg(gold_table, mode="append")
            
            result = {
                "status": "success",
                "gold_table": f"{self.gold_namespace}.daily_system_health",
                "processing_date": processing_date,
                "records_created": len(health_df),
                "processing_time": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info("Created system health dashboard for %s: %d records", 
                       processing_date, len(health_df))
            return result
            
        except Exception as e:
            error_msg = f"Failed to create system health dashboard for {processing_date}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "processing_date": processing_date
            }
    
    def _load_silver_data(self, table_name: str, processing_date: str) -> Optional[daft.DataFrame]:
        """
        Load silver data for a specific date.
        
        Args:
            table_name: Silver table name
            processing_date: Date to process (YYYY-MM-DD)
            
        Returns:
            Daft DataFrame with silver data
        """
        try:
            silver_table = self.catalog_manager.get_table(table_name, self.silver_namespace)
            
            # Parse processing date
            date_parts = processing_date.split('-')
            year, month = int(date_parts[0]), int(date_parts[1])
            
            # Read data with date filter
            df = daft.read_iceberg(silver_table)
            df = df.where(
                (df["year"] == year) & 
                (df["month"] == month)
            )
            
            return df
            
        except Exception as e:
            logger.error("Failed to load silver data for %s on %s: %s", 
                        table_name, processing_date, str(e))
            return None
    
    def _create_user_behavior_aggregations(self, df: daft.DataFrame, 
                                         processing_date: str) -> daft.DataFrame:
        """Create user behavior aggregations."""
        try:
            # Parse processing date
            date_obj = datetime.strptime(processing_date, '%Y-%m-%d')
            
            # Example aggregations (would need actual user event parsing)
            summary_df = daft.from_pylist([{
                "date": processing_date,
                "year": date_obj.year,
                "total_events": len(df),
                "unique_users": 100,  # Placeholder - would calculate from actual data
                "avg_session_duration": 300.0,  # Placeholder
                "bounce_rate": 0.25,  # Placeholder
                "conversion_rate": 0.05,  # Placeholder
                "created_at": datetime.now(timezone.utc)
            }])
            
            return summary_df
            
        except Exception as e:
            logger.error("Failed to create user behavior aggregations: %s", str(e))
            raise
    
    def _create_transaction_metrics(self, df: daft.DataFrame, 
                                  processing_date: str) -> daft.DataFrame:
        """Create transaction metrics and financial KPIs."""
        try:
            # Parse processing date
            date_obj = datetime.strptime(processing_date, '%Y-%m-%d')
            
            # Example transaction metrics (would need actual transaction parsing)
            metrics_df = daft.from_pylist([{
                "date": processing_date,
                "year": date_obj.year,
                "total_transactions": len(df),
                "total_revenue": 10000.0,  # Placeholder
                "avg_transaction_value": 100.0,  # Placeholder
                "successful_transactions": len(df) * 0.95,  # Placeholder
                "failed_transactions": len(df) * 0.05,  # Placeholder
                "success_rate": 0.95,  # Placeholder
                "created_at": datetime.now(timezone.utc)
            }])
            
            return metrics_df
            
        except Exception as e:
            logger.error("Failed to create transaction metrics: %s", str(e))
            raise
    
    def _create_system_health_metrics(self, df: daft.DataFrame, 
                                    processing_date: str) -> daft.DataFrame:
        """Create system health metrics."""
        try:
            # Parse processing date
            date_obj = datetime.strptime(processing_date, '%Y-%m-%d')
            
            # Example system health metrics (would need actual metrics parsing)
            health_df = daft.from_pylist([{
                "date": processing_date,
                "year": date_obj.year,
                "total_metrics": len(df),
                "avg_cpu_usage": 65.0,  # Placeholder
                "avg_memory_usage": 70.0,  # Placeholder
                "avg_disk_usage": 45.0,  # Placeholder
                "error_count": 5,  # Placeholder
                "warning_count": 15,  # Placeholder
                "uptime_percentage": 99.9,  # Placeholder
                "created_at": datetime.now(timezone.utc)
            }])
            
            return health_df
            
        except Exception as e:
            logger.error("Failed to create system health metrics: %s", str(e))
            raise
    
    def _get_or_create_gold_table(self, table_name: str, schema: Schema) -> Table:
        """Get or create gold table with specified schema."""
        try:
            return self.catalog_manager.get_table(table_name, self.gold_namespace)
        except Exception:
            logger.info("Creating new gold table: %s.%s", self.gold_namespace, table_name)
            return self.catalog_manager.create_gold_table(table_name, schema, self.gold_namespace)
    
    def _create_user_summary_schema(self) -> Schema:
        """Create schema for daily user summary table."""
        return Schema(
            NestedField(1, "date", StringType(), required=True),
            NestedField(2, "year", LongType(), required=True),
            NestedField(3, "total_events", LongType(), required=True),
            NestedField(4, "unique_users", LongType(), required=True),
            NestedField(5, "avg_session_duration", DoubleType(), required=True),
            NestedField(6, "bounce_rate", DoubleType(), required=True),
            NestedField(7, "conversion_rate", DoubleType(), required=True),
            NestedField(8, "created_at", TimestampType(), required=True),
        )
    
    def _create_transaction_metrics_schema(self) -> Schema:
        """Create schema for transaction metrics table."""
        return Schema(
            NestedField(1, "date", StringType(), required=True),
            NestedField(2, "year", LongType(), required=True),
            NestedField(3, "total_transactions", LongType(), required=True),
            NestedField(4, "total_revenue", DoubleType(), required=True),
            NestedField(5, "avg_transaction_value", DoubleType(), required=True),
            NestedField(6, "successful_transactions", LongType(), required=True),
            NestedField(7, "failed_transactions", LongType(), required=True),
            NestedField(8, "success_rate", DoubleType(), required=True),
            NestedField(9, "created_at", TimestampType(), required=True),
        )
    
    def _create_system_health_schema(self) -> Schema:
        """Create schema for system health table."""
        return Schema(
            NestedField(1, "date", StringType(), required=True),
            NestedField(2, "year", LongType(), required=True),
            NestedField(3, "total_metrics", LongType(), required=True),
            NestedField(4, "avg_cpu_usage", DoubleType(), required=True),
            NestedField(5, "avg_memory_usage", DoubleType(), required=True),
            NestedField(6, "avg_disk_usage", DoubleType(), required=True),
            NestedField(7, "error_count", LongType(), required=True),
            NestedField(8, "warning_count", LongType(), required=True),
            NestedField(9, "uptime_percentage", DoubleType(), required=True),
            NestedField(10, "created_at", TimestampType(), required=True),
        )


class GoldLayerManager:
    """
    Manager for gold layer operations.
    Coordinates multiple gold processors and provides high-level operations.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize gold layer manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.num_processors = self.config.get('num_gold_processors', 2)
        self.processors = []
        
        # Initialize Ray actors
        self._initialize_processors()
        
        logger.info("Initialized GoldLayerManager with %d processors", self.num_processors)
    
    def _initialize_processors(self):
        """Initialize gold processor Ray actors."""
        for i in range(self.num_processors):
            processor = GoldProcessor.remote(self.config)
            self.processors.append(processor)
    
    def process_daily_analytics(self, processing_date: str) -> List[ray.ObjectRef]:
        """
        Process all daily analytics for a given date.
        
        Args:
            processing_date: Date to process (YYYY-MM-DD format)
            
        Returns:
            List of Ray ObjectRefs for processing results
        """
        results = []
        
        # Process user summary
        processor = self.processors[0]
        results.append(processor.create_daily_user_summary.remote(processing_date))
        
        # Process transaction metrics
        processor = self.processors[1 % len(self.processors)]
        results.append(processor.create_transaction_metrics.remote(processing_date))
        
        # Process system health
        processor = self.processors[2 % len(self.processors)]
        results.append(processor.create_system_health_dashboard.remote(processing_date))
        
        return results
    
    def shutdown(self):
        """Shutdown all gold processors."""
        for processor in self.processors:
            ray.kill(processor)
        self.processors.clear()
        logger.info("Shutdown GoldLayerManager")


def create_gold_layer_manager(config: Optional[Dict[str, Any]] = None) -> GoldLayerManager:
    """
    Factory function to create gold layer manager.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        GoldLayerManager instance
    """
    return GoldLayerManager(config)
