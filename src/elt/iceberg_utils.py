"""
Iceberg utilities for catalog management, table operations, and configuration.
Supports <PERSON><PERSON><PERSON> catalog with MinIO storage backend.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import daft
from pyiceberg.catalog import load_catalog
from pyiceberg.table import Table
from pyiceberg.schema import Schema
from pyiceberg.types import (
    NestedField, StringType, LongType, TimestampType, 
    StructType, BooleanType, DoubleType
)
from pyiceberg.partitioning import PartitionSpec, PartitionField
from pyiceberg.transforms import YearTransform, MonthTransform, DayTransform
from minio import Minio
from minio.error import S3Error

logger = logging.getLogger(__name__)


class IcebergCatalogManager:
    """Manages Iceberg catalog operations with <PERSON><PERSON><PERSON> and <PERSON><PERSON>."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Iceberg catalog manager.
        
        Args:
            config: Configuration dictionary. If None, loads from environment.
        """
        self.config = config or self._load_config_from_env()
        self.catalog = None
        self.minio_client = None
        self._initialize_catalog()
        self._initialize_minio()
    
    def _load_config_from_env(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        return {
            # Nessie configuration
            'nessie_uri': os.getenv('NESSIE_URI', 'http://localhost:19120/api/v1'),
            'nessie_ref': os.getenv('NESSIE_REF', 'main'),
            'nessie_auth_type': os.getenv('NESSIE_AUTH_TYPE', 'NONE'),
            
            # Iceberg configuration
            'warehouse_path': os.getenv('ICEBERG_WAREHOUSE_PATH', 's3a://data-lake/warehouse'),
            'catalog_type': os.getenv('ICEBERG_CATALOG_TYPE', 'nessie'),
            
            # MinIO configuration
            'minio_endpoint': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
            'minio_access_key': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
            'minio_secret_key': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
            'minio_secure': os.getenv('MINIO_SECURE', 'false').lower() == 'true',
            'minio_bucket': os.getenv('MINIO_BUCKET_NAME', 'data-lake'),
        }
    
    def _initialize_catalog(self):
        """Initialize Iceberg catalog with Nessie."""
        try:
            catalog_config = {
                'type': 'nessie',
                'uri': self.config['nessie_uri'],
                'ref': self.config['nessie_ref'],
                'authentication.type': self.config['nessie_auth_type'],
                'warehouse': self.config['warehouse_path'],
                's3.endpoint': f"http://{self.config['minio_endpoint']}",
                's3.access-key-id': self.config['minio_access_key'],
                's3.secret-access-key': self.config['minio_secret_key'],
                's3.path-style-access': 'true',
            }
            
            self.catalog = load_catalog('nessie_catalog', **catalog_config)
            logger.info(f"Initialized Iceberg catalog with Nessie: {self.config['nessie_uri']}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Iceberg catalog: {e}")
            raise
    
    def _initialize_minio(self):
        """Initialize MinIO client."""
        try:
            self.minio_client = Minio(
                self.config['minio_endpoint'],
                access_key=self.config['minio_access_key'],
                secret_key=self.config['minio_secret_key'],
                secure=self.config['minio_secure']
            )
            
            # Ensure bucket exists
            bucket_name = self.config['minio_bucket']
            if not self.minio_client.bucket_exists(bucket_name):
                self.minio_client.make_bucket(bucket_name)
                logger.info(f"Created MinIO bucket: {bucket_name}")
            else:
                logger.info(f"MinIO bucket exists: {bucket_name}")
                
        except S3Error as e:
            logger.error(f"Failed to initialize MinIO client: {e}")
            raise
    
    def create_bronze_table(self, table_name: str, namespace: str = 'bronze') -> Table:
        """
        Create a bronze layer table with standard schema for raw Kafka data.
        
        Args:
            table_name: Name of the table
            namespace: Namespace/database name
            
        Returns:
            Created Iceberg table
        """
        full_table_name = f"{namespace}.{table_name}"
        
        # Define schema for bronze tables (raw Kafka data)
        schema = Schema(
            NestedField(1, "key", StringType(), required=False),
            NestedField(2, "value", StringType(), required=True),
            NestedField(3, "topic", StringType(), required=True),
            NestedField(4, "partition", LongType(), required=True),
            NestedField(5, "offset", LongType(), required=True),
            NestedField(6, "timestamp", TimestampType(), required=True),
            NestedField(7, "kafka_timestamp", TimestampType(), required=True),
            NestedField(8, "processing_time", TimestampType(), required=True),
            NestedField(9, "year", LongType(), required=True),
            NestedField(10, "month", LongType(), required=True),
            NestedField(11, "day", LongType(), required=True),
        )
        
        # Define partitioning by year, month, day
        partition_spec = PartitionSpec(
            PartitionField(source_id=9, field_id=1000, transform=YearTransform(), name="year"),
            PartitionField(source_id=10, field_id=1001, transform=MonthTransform(), name="month"),
            PartitionField(source_id=11, field_id=1002, transform=DayTransform(), name="day"),
        )
        
        try:
            # Create namespace if it doesn't exist
            try:
                self.catalog.create_namespace(namespace)
                logger.info(f"Created namespace: {namespace}")
            except Exception:
                logger.info(f"Namespace already exists: {namespace}")
            
            # Create table
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                partition_spec=partition_spec,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd',
                    'write.metadata.delete-after-commit.enabled': 'true',
                    'write.metadata.previous-versions-max': '5',
                }
            )
            
            logger.info(f"Created bronze table: {full_table_name}")
            return table
            
        except Exception as e:
            logger.error(f"Failed to create bronze table {full_table_name}: {e}")
            raise
    
    def create_silver_table(self, table_name: str, schema: Schema, 
                          namespace: str = 'silver') -> Table:
        """
        Create a silver layer table with custom schema.
        
        Args:
            table_name: Name of the table
            schema: Iceberg schema for the table
            namespace: Namespace/database name
            
        Returns:
            Created Iceberg table
        """
        full_table_name = f"{namespace}.{table_name}"
        
        # Default partitioning by year and month for silver tables
        partition_spec = PartitionSpec(
            PartitionField(source_id=1, field_id=1000, transform=YearTransform(), name="year"),
            PartitionField(source_id=2, field_id=1001, transform=MonthTransform(), name="month"),
        )
        
        try:
            # Create namespace if it doesn't exist
            try:
                self.catalog.create_namespace(namespace)
                logger.info(f"Created namespace: {namespace}")
            except Exception:
                logger.info(f"Namespace already exists: {namespace}")
            
            # Create table
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                partition_spec=partition_spec,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd',
                    'write.metadata.delete-after-commit.enabled': 'true',
                    'write.metadata.previous-versions-max': '10',
                }
            )
            
            logger.info(f"Created silver table: {full_table_name}")
            return table
            
        except Exception as e:
            logger.error(f"Failed to create silver table {full_table_name}: {e}")
            raise
    
    def create_gold_table(self, table_name: str, schema: Schema, 
                         namespace: str = 'gold') -> Table:
        """
        Create a gold layer table with custom schema.
        
        Args:
            table_name: Name of the table
            schema: Iceberg schema for the table
            namespace: Namespace/database name
            
        Returns:
            Created Iceberg table
        """
        full_table_name = f"{namespace}.{table_name}"
        
        # Minimal partitioning for gold tables (usually by year)
        partition_spec = PartitionSpec(
            PartitionField(source_id=1, field_id=1000, transform=YearTransform(), name="year"),
        )
        
        try:
            # Create namespace if it doesn't exist
            try:
                self.catalog.create_namespace(namespace)
                logger.info(f"Created namespace: {namespace}")
            except Exception:
                logger.info(f"Namespace already exists: {namespace}")
            
            # Create table
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                partition_spec=partition_spec,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd',
                    'write.metadata.delete-after-commit.enabled': 'true',
                    'write.metadata.previous-versions-max': '20',
                }
            )
            
            logger.info(f"Created gold table: {full_table_name}")
            return table
            
        except Exception as e:
            logger.error(f"Failed to create gold table {full_table_name}: {e}")
            raise
    
    def get_table(self, table_name: str, namespace: str) -> Table:
        """
        Get an existing Iceberg table.
        
        Args:
            table_name: Name of the table
            namespace: Namespace/database name
            
        Returns:
            Iceberg table
        """
        full_table_name = f"{namespace}.{table_name}"
        try:
            return self.catalog.load_table(full_table_name)
        except Exception as e:
            logger.error(f"Failed to load table {full_table_name}: {e}")
            raise
    
    def list_tables(self, namespace: str) -> List[str]:
        """
        List all tables in a namespace.
        
        Args:
            namespace: Namespace/database name
            
        Returns:
            List of table names
        """
        try:
            return self.catalog.list_tables(namespace)
        except Exception as e:
            logger.error(f"Failed to list tables in namespace {namespace}: {e}")
            raise
    
    def table_exists(self, table_name: str, namespace: str) -> bool:
        """
        Check if a table exists.
        
        Args:
            table_name: Name of the table
            namespace: Namespace/database name
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            self.get_table(table_name, namespace)
            return True
        except Exception:
            return False


def create_standard_bronze_schema() -> Schema:
    """Create standard schema for bronze layer tables."""
    return Schema(
        NestedField(1, "key", StringType(), required=False),
        NestedField(2, "value", StringType(), required=True),
        NestedField(3, "topic", StringType(), required=True),
        NestedField(4, "partition", LongType(), required=True),
        NestedField(5, "offset", LongType(), required=True),
        NestedField(6, "timestamp", TimestampType(), required=True),
        NestedField(7, "kafka_timestamp", TimestampType(), required=True),
        NestedField(8, "processing_time", TimestampType(), required=True),
        NestedField(9, "year", LongType(), required=True),
        NestedField(10, "month", LongType(), required=True),
        NestedField(11, "day", LongType(), required=True),
    )


def get_iceberg_catalog_manager() -> IcebergCatalogManager:
    """Get a singleton instance of IcebergCatalogManager."""
    if not hasattr(get_iceberg_catalog_manager, '_instance'):
        get_iceberg_catalog_manager._instance = IcebergCatalogManager()
    return get_iceberg_catalog_manager._instance
