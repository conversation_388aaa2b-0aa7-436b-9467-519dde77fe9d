[{"consumer_name": "user_events_consumer", "topic_name": "user-events", "number_of_workers": 3, "enable_auto_commit": false, "bootstrap_servers": "localhost:9092", "key_deserializer": "STRING_DES", "value_deserializer": "STRING_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 50, "max_poll_interval_ms": 60000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "num_retries": 3, "retry_delay_seconds": 1, "add_kafka_metadata": true, "add_processing_time": true, "parse_json_value": true, "flatten_json": false, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 1000, "batch_timeout_seconds": 30, "namespace": "bronze", "auto_create_tables": true, "max_retries": 3}, "dlq_config": {"bootstrap_servers": "localhost:9092", "topic_name": "user-events-dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}, {"consumer_name": "transaction_logs_consumer", "topic_name": "transaction-logs", "number_of_workers": 2, "enable_auto_commit": false, "bootstrap_servers": "localhost:9092", "key_deserializer": "STRING_DES", "value_deserializer": "STRING_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 30, "max_poll_interval_ms": 60000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "num_retries": 3, "retry_delay_seconds": 1, "add_kafka_metadata": true, "add_processing_time": true, "parse_json_value": true, "flatten_json": false, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 500, "batch_timeout_seconds": 20, "namespace": "bronze", "auto_create_tables": true, "max_retries": 3}, "dlq_config": {"bootstrap_servers": "localhost:9092", "topic_name": "transaction-logs-dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}, {"consumer_name": "system_metrics_consumer", "topic_name": "system-metrics", "number_of_workers": 2, "enable_auto_commit": false, "bootstrap_servers": "localhost:9092", "key_deserializer": "STRING_DES", "value_deserializer": "STRING_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 100, "max_poll_interval_ms": 60000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "num_retries": 3, "retry_delay_seconds": 1, "add_kafka_metadata": true, "add_processing_time": true, "parse_json_value": true, "flatten_json": false, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 2000, "batch_timeout_seconds": 60, "namespace": "bronze", "auto_create_tables": true, "max_retries": 3}, "dlq_config": {"bootstrap_servers": "localhost:9092", "topic_name": "system-metrics-dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}]