# Iceberg Configuration for <PERSON><PERSON><PERSON> and MinIO Storage

# <PERSON><PERSON>ie <PERSON>alog Configuration
nessie:
  uri: "http://localhost:19120/api/v1"
  ref: "main"
  auth_type: "NONE"
  
# MinIO S3-compatible Storage Configuration
minio:
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  secure: false
  bucket_name: "data-lake"
  
# Iceberg Warehouse Configuration
iceberg:
  warehouse_path: "s3a://data-lake/warehouse"
  catalog_type: "nessie"
  
  # Table Properties
  table_properties:
    bronze:
      write.format.default: "parquet"
      write.parquet.compression-codec: "zstd"
      write.metadata.delete-after-commit.enabled: "true"
      write.metadata.previous-versions-max: "5"
      
    silver:
      write.format.default: "parquet"
      write.parquet.compression-codec: "zstd"
      write.metadata.delete-after-commit.enabled: "true"
      write.metadata.previous-versions-max: "10"
      
    gold:
      write.format.default: "parquet"
      write.parquet.compression-codec: "zstd"
      write.metadata.delete-after-commit.enabled: "true"
      write.metadata.previous-versions-max: "20"

# Namespace Configuration
namespaces:
  bronze: "bronze"
  silver: "silver"
  gold: "gold"

# Partitioning Strategies
partitioning:
  bronze:
    - field: "year"
      transform: "year"
    - field: "month"
      transform: "month"
    - field: "day"
      transform: "day"
      
  silver:
    - field: "year"
      transform: "year"
    - field: "month"
      transform: "month"
      
  gold:
    - field: "year"
      transform: "year"

# Data Quality Configuration
data_quality:
  bronze:
    min_record_size: 10
    max_record_size: 1000000
    
  silver:
    min_record_size: 10
    max_record_size: 1000000
    duplicate_window_hours: 24
    null_threshold: 0.1
    
  gold:
    completeness_threshold: 0.95
    accuracy_threshold: 0.99

# Processing Configuration
processing:
  bronze:
    batch_size: 1000
    batch_timeout_seconds: 30
    num_processors: 2
    
  silver:
    num_processors: 2
    processing_window_hours: 24
    
  gold:
    num_processors: 2
    aggregation_window_hours: 24
